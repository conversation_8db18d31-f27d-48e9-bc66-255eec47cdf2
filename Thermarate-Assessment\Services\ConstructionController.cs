using AutoMapper;
using AutoMapper.QueryableExtensions;
using Ganss.Excel;
using Kendo.DynamicLinq;
using Newtonsoft.Json;
using RediSoftware.BusinessLogic;
using RediSoftware.Common;
using RediSoftware.Dtos;
using RediSoftware.Helpers;
using RediSoftware.Models;
using RediSoftware.Redi_Utility;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using AuthorizeAttribute = System.Web.Mvc.AuthorizeAttribute;
using HttpGetAttribute = System.Web.Http.HttpGetAttribute;
using HttpPostAttribute = System.Web.Http.HttpPostAttribute;
using RoutePrefixAttribute = System.Web.Http.RoutePrefixAttribute;

namespace RediSoftware.Services
{

    public enum ConstructionCategory
    {
        /// <summary> Ceiling (Neighbour Above)                  </summary>
        CeilingNeighbourAbove,
        /// <summary> Ceiling (Roof/Roof Space Above)            </summary>
        CeilingRoofAbove,
        /// <summary> Exterior Door                              </summary>
        ExteriorDoor,
        /// <summary> Exterior Floor (Suspended)                 </summary>
        ExteriorFloor,
        /// <summary> Exterior Floor (Elevated)                  </summary>
        ExteriorFloorElevated,
        /// <summary> Exterior Glazing                           </summary>
        ExteriorGlazing,
        /// <summary> Exterior Wall                              </summary>
        ExteriorWall,
        /// <summary> Exterior Floor (Connected to Ground)       </summary>
        GroundFloor,
        /// <summary> Horizontal Opening                         </summary>
        HorizontalOpening,
        /// <summary> Interior Door                              </summary>
        InteriorDoor,
        /// <summary> Interior Glazing                           </summary>
        InteriorGlazing,
        /// <summary> Interior Wall (Partition)                  </summary>
        InteriorWall,
        /// <summary> Interior Wall (Adjacent to Neighbour)      </summary>
        InteriorWallAdjacentToNeighbour,
        /// <summary> Interior Wall (Adjacent to Roof Space)     </summary>
        InteriorWallAdjacentToRoofSpace,
        /// <summary> Interior Wall (Adjacent to Subfloor Space) </summary>
        InteriorWallAdjacentToSubfloorSpace,
        /// <summary> Intermediate Floor                         </summary>
        IntermediateFloor,
        /// <summary> Intermediate Floor (Neighbour Below)       </summary>
        IntermediateFloorNeighbourBelow,
        /// <summary> Roof                                       </summary>
        Roof,
        /// <summary> Roof Window                                </summary>
        RoofWindow,
        /// <summary> Skylight                                   </summary>
        Skylight,
        /// <summary> Subfloor Wall                              </summary>
        SubfloorWall,
        /// <summary> Vertical Opening                           </summary>
        VerticalOpening
    }

    public enum ConstructionSubCategory
    {
        /// <summary> Autoclaved Aerated Concrete Wall   </summary>
        AutoclavedAeratedConcreteWall,
        /// <summary> Brick Cavity Wall                  </summary>
        BrickCavityWall,
        /// <summary> Brick Veneer Wall                  </summary>
        BrickVeneerWall,
        /// <summary> Concrete Block Wall                </summary>
        ConcreteBlockWall,
        /// <summary> Concrete Panel Wall                </summary>
        ConcretePanelWall,
        /// <summary> Concrete Slab Roof (No Roof Space) </summary>
        ConcreteSlabRoof,
        /// <summary> Double Brick (No Cavity) Wall      </summary>
        DoubleBrickWall,
        /// <summary> Flat Framed Roof (No Roof Space)   </summary>
        FlatFramedRoof,
        /// <summary> Heavyweight Floor                  </summary>
        HeavyweightFloor,
        /// <summary> Insulated Ceiling                  </summary>
        InsulatedCeiling,
        /// <summary> Insulated Concrete Form Wall       </summary>
        InsulatedConcreteFormWall,
        /// <summary> Lightweight Floor                  </summary>
        LightweightFloor,
        /// <summary> Lightweight Wall                   </summary>
        LightweightWall,
        /// <summary> Mediumweight Floor                 </summary>
        MediumweightFloor,
        /// <summary> Metal Sheet Roof (With Roof Space) </summary>
        MetalSheetRoof,
        /// <summary> Other (Heavyweight) Wall           </summary>
        OtherHeavyweightWall,
        /// <summary> Other (Mediumweight) Wall          </summary>
        OtherMediumweightWall,
        /// <summary> Reverse Brick Veneer Wall          </summary>
        ReverseBrickVeneerWall,
        /// <summary> Single Brick Wall                  </summary>
        SingleBrickWall,
        /// <summary> Structural Insulated Panel Wall    </summary>
        StructuralInsulatedPanelWall,
        /// <summary> Tiled Roof (With Roof Space)       </summary>
        TiledRoof,
        /// <summary> Uninsulated Ceiling                </summary>
        UninsulatedCeiling
    }

    /// <summary>
        /// Responsible for creating and updating 'Construction Templates' (Both Surfaces, Openings, and sub-categories).
        ///
        /// Due to the simplicity (and, at least for the moment, non-maleable nature) of our construction enums (e.g. glass type,
        /// frame material, etc) this controller is also responsible for RETREIVING those lists, but NOT updating them (as they are
        /// not designed to be updated by clients).
        /// </summary>
        [Authorize]
    [RoutePrefix("api/Construction")]
    public class ConstructionController : ApiController
    {
        private readonly IMapper _mapper;
        private readonly IUnitOfWork _unitOfWork;
        private Construction _constructionContext;

        public ConstructionController(IUnitOfWork unitOfWork,
                                      IMapper mapper,
                                      Construction constructionFactory)
        {
            _mapper = mapper;
            _unitOfWork = unitOfWork;
            _constructionContext = constructionFactory;
        }

        ///// <summary>
        ///// Return a single record based on its Id
        ///// </summary>
        public IHttpActionResult Get([FromUri] Guid constructionId, string type)
        {
            type = type.ToLower();

            if (type == "surface")
                return Ok(_constructionContext.Get<SurfaceTemplateDto>(constructionId));
            else if (type == "opening")
                return Ok(_constructionContext.Get<OpeningTemplateDto>(constructionId));
            else
                throw new NotImplementedException("Can only GET 'surface' or 'opening'.");

        }

        /// <summary>
        /// Things are more complicated than they should be as usual, and now our 'surfaces' and
        /// 'opening' delineation is not sufficient. This V2 version splits along different lines than V1.
        /// </summary>
        public IHttpActionResult GetV2(string type, string fromDate = null, string toDate = null, bool? isDeleted = false, [FromUri] PagingParameters paging = null)
        {
            type = type.ToLower();

            var s = _constructionContext.GetListV2(type, fromDate, toDate, isDeleted, paging);
            return Ok(s);
        }

        [HttpPost]
        public IHttpActionResult GetMultiFiltered([FromBody] ConstructionFilterDataDto filterData)
        {
            // Use the unified view for both construction and opening types
            var query = _unitOfWork.Context.RSS_SurfaceTemplateOpeningTemplateView.Where(x => !x.Deleted);

            // Filter by type if specified - use same logic as GetListV2
            if (!string.IsNullOrEmpty(filterData.type))
            {
                if (filterData.type.ToLower() == "construction")
                {
                    // Use same filtering logic as GetListV2 method
                    query = query.Where(x => x.Type == "surface" &&
                                           x.ConstructionCategoryCode != "ExteriorDoor" &&
                                           x.ConstructionCategoryCode != "InteriorDoor" &&
                                           x.ConstructionCategoryCode != "VerticalOpening" &&
                                           x.ConstructionCategoryCode != "HorizontalOpening");
                }
                else if (filterData.type.ToLower() == "opening")
                {
                    // Use same filtering logic as GetListV2 method for openings
                    query = query.Where(x => x.Type != "surface" ||
                                           (x.ConstructionCategoryCode == "ExteriorDoor" ||
                                            x.ConstructionCategoryCode == "InteriorDoor" ||
                                            x.ConstructionCategoryCode == "VerticalOpening" ||
                                            x.ConstructionCategoryCode == "HorizontalOpening"));
                }
            }

            if (filterData.appliedFilters != null)
                query = AlterQueryFromMultiFiltersForView(query, filterData);

            var results = query
                .ProjectTo<SurfaceOpeningViewDto>(_mapper.ConfigurationProvider)
                .ToDataSourceResult(filterData.paging.PageSize, filterData.paging.Skip, filterData.paging?.Sort ?? new List<string>() { "CreatedOn@@desc" }, filterData.paging?.Filter, filterData.paging?.Aggregate, filterData.paging?.Export);

            return Ok(results);
        }

        [HttpPost]
        public IHttpActionResult GetMultiFilterOptions([FromBody] ConstructionFilterOptionsDto request)
        {
            var filterOptions = new Dictionary<string, List<FilterOption>>();

            // Use the unified view for both construction and opening types
            var query = _unitOfWork.Context.RSS_SurfaceTemplateOpeningTemplateView.Where(x => !x.Deleted);

            // Filter by type if specified - use same logic as GetListV2
            if (!string.IsNullOrEmpty(request.type))
            {
                if (request.type.ToLower() == "construction")
                {
                    // Use same filtering logic as GetListV2 method
                    query = query.Where(x => x.Type == "surface" &&
                                           x.ConstructionCategoryCode != "ExteriorDoor" &&
                                           x.ConstructionCategoryCode != "InteriorDoor" &&
                                           x.ConstructionCategoryCode != "VerticalOpening" &&
                                           x.ConstructionCategoryCode != "HorizontalOpening");
                }
                else if (request.type.ToLower() == "opening")
                {
                    // Use same filtering logic as GetListV2 method for openings
                    query = query.Where(x => x.Type != "surface" ||
                                           (x.ConstructionCategoryCode == "ExteriorDoor" ||
                                            x.ConstructionCategoryCode == "InteriorDoor" ||
                                            x.ConstructionCategoryCode == "VerticalOpening" ||
                                            x.ConstructionCategoryCode == "HorizontalOpening"));
                }
            }

            // Get Category options
            var categoryOptions = new List<FilterOption> { new FilterOption { name = "Any", value = "Any" } };
            var categoryData = query
                .Where(x => !string.IsNullOrEmpty(x.ConstructionCategoryTitle))
                .GroupBy(x => x.ConstructionCategoryTitle)
                .Select(g => new FilterOption { name = g.Key, value = g.Key, count = g.Count() })
                .OrderBy(x => x.name)
                .ToList();
            categoryOptions.AddRange(categoryData);
            filterOptions["constructionCategoryTitle"] = categoryOptions;

            // Get Construction Sub Category options (for construction type)
            if (request.type.ToLower() == "construction")
            {
                var subCategoryOptions = new List<FilterOption> { new FilterOption { name = "Any", value = "Any" } };
                var subCategoryData = query
                    .Where(x => !string.IsNullOrEmpty(x.ConstructionSubCategoryTitle))
                    .GroupBy(x => x.ConstructionSubCategoryTitle)
                    .Select(g => new FilterOption { name = g.Key, value = g.Key, count = g.Count() })
                    .OrderBy(x => x.name)
                    .ToList();
                subCategoryOptions.AddRange(subCategoryData);
                filterOptions["constructionSubCategoryTitle"] = subCategoryOptions;
            }
            else if (request.type.ToLower() == "opening")
            {
                // Get Opening Style options (for opening type)
                var openingStyleOptions = new List<FilterOption> { new FilterOption { name = "Any", value = "Any" } };
                var openingStyleData = query
                    .Where(x => !string.IsNullOrEmpty(x.OpeningStyleTitle))
                    .GroupBy(x => x.OpeningStyleTitle)
                    .Select(g => new FilterOption { name = g.Key, value = g.Key, count = g.Count() })
                    .OrderBy(x => x.name)
                    .ToList();
                openingStyleOptions.AddRange(openingStyleData);
                filterOptions["openingStyleTitle"] = openingStyleOptions;

                // Get Glass Type options (for opening type)
                var glassTypeOptions = new List<FilterOption> { new FilterOption { name = "Any", value = "Any" } };

                // Check if there are any null or empty values first
                var hasNullOrEmptyGlassType = query
                    .Any(x => string.IsNullOrEmpty(x.GlassTypeTitle) || x.GlassTypeTitle == "Not Specified");

                // Add "Not Specified" at the top (after "Any") if there are null/empty values
                if (hasNullOrEmptyGlassType)
                {
                    glassTypeOptions.Add(new FilterOption { name = "Not Specified", value = "Not Specified" });
                }

                var glassTypeData = query
                    .Where(x => !string.IsNullOrEmpty(x.GlassTypeTitle) && x.GlassTypeTitle != "Not Specified")
                    .GroupBy(x => x.GlassTypeTitle)
                    .Select(g => new FilterOption { name = g.Key, value = g.Key, count = g.Count() })
                    .OrderBy(x => x.name)
                    .ToList();
                glassTypeOptions.AddRange(glassTypeData);
                filterOptions["glassTypeTitle"] = glassTypeOptions;

                // Get Glass Colour options (for opening type)
                var glassColourOptions = new List<FilterOption> { new FilterOption { name = "Any", value = "Any" } };

                // Check if there are any null or empty values first
                var hasNullOrEmptyGlassColour = query
                    .Any(x => string.IsNullOrEmpty(x.GlassColourTitle) || x.GlassColourTitle == "Not Specified");

                // Add "Not Specified" at the top (after "Any") if there are null/empty values
                if (hasNullOrEmptyGlassColour)
                {
                    glassColourOptions.Add(new FilterOption { name = "Not Specified", value = "Not Specified" });
                }

                var glassColourData = query
                    .Where(x => !string.IsNullOrEmpty(x.GlassColourTitle) && x.GlassColourTitle != "Not Specified")
                    .GroupBy(x => x.GlassColourTitle)
                    .Select(g => new FilterOption { name = g.Key, value = g.Key, count = g.Count() })
                    .OrderBy(x => x.name)
                    .ToList();
                glassColourOptions.AddRange(glassColourData);
                filterOptions["glassColourTitle"] = glassColourOptions;

                // Get Low-E Coating options (for opening type)
                var lowEOptions = new List<FilterOption> { new FilterOption { name = "Any", value = "Any" } };

                // Check if there are any null or empty values first
                var hasNullOrEmptyLowE = query
                    .Any(x => string.IsNullOrEmpty(x.LowECoating) || x.LowECoating == "Not Specified");

                // Add "Not Specified" at the top (after "Any") if there are null/empty values
                if (hasNullOrEmptyLowE)
                {
                    lowEOptions.Add(new FilterOption { name = "Not Specified", value = "Not Specified" });
                }

                var lowEData = query
                    .Where(x => !string.IsNullOrEmpty(x.LowECoating) && x.LowECoating != "Not Specified")
                    .GroupBy(x => x.LowECoating)
                    .Select(g => new FilterOption { name = g.Key, value = g.Key, count = g.Count() })
                    .OrderBy(x => x.name)
                    .ToList();
                lowEOptions.AddRange(lowEData);
                filterOptions["lowECoating"] = lowEOptions;

                // Get Frame Material options (for opening type)
                var frameMaterialOptions = new List<FilterOption> { new FilterOption { name = "Any", value = "Any" } };

                // Check if there are any null or empty values first
                var hasNullOrEmptyFrameMaterial = query
                    .Any(x => string.IsNullOrEmpty(x.FrameMaterialTitle) || x.FrameMaterialTitle == "Not Specified");

                // Add "Not Specified" at the top (after "Any") if there are null/empty values
                if (hasNullOrEmptyFrameMaterial)
                {
                    frameMaterialOptions.Add(new FilterOption { name = "Not Specified", value = "Not Specified" });
                }

                var frameMaterialData = query
                    .Where(x => !string.IsNullOrEmpty(x.FrameMaterialTitle) && x.FrameMaterialTitle != "Not Specified")
                    .GroupBy(x => x.FrameMaterialTitle)
                    .Select(g => new FilterOption { name = g.Key, value = g.Key, count = g.Count() })
                    .OrderBy(x => x.name)
                    .ToList();
                frameMaterialOptions.AddRange(frameMaterialData);
                filterOptions["frameMaterialTitle"] = frameMaterialOptions;
            }

            // Add Insulation filter options (only for construction type, not openings)
            if (request.type.ToLower() == "construction")
            {
                var insulationOptions = new List<FilterOption> { new FilterOption { name = "Any", value = "Any" } };

                // Check if there are any null or empty values first
                var hasNullOrEmpty = query
                    .Any(x => string.IsNullOrEmpty(x.InsulationDescription));

                // Add "Not Specified" at the top (after "Any") if there are null/empty values
                if (hasNullOrEmpty)
                {
                    insulationOptions.Add(new FilterOption { name = "Not Specified", value = "Not Specified" });
                }

                // Get distinct insulation descriptions
                var insulationData = query
                    .Where(x => !string.IsNullOrEmpty(x.InsulationDescription))
                    .GroupBy(x => x.InsulationDescription)
                    .Select(g => new FilterOption { name = g.Key, value = g.Key, count = g.Count() })
                    .OrderBy(x => x.name)
                    .ToList();

                insulationOptions.AddRange(insulationData);

                filterOptions["insulationDescription"] = insulationOptions;
            }

            // Add Manufacturer filter options (for both construction and opening types)
            var manufacturerOptions = new List<FilterOption> { new FilterOption { name = "Any", value = "Any" } };

            // Check if there are any null or empty values first
            var hasNullOrEmptyManufacturer = query
                .Any(x => string.IsNullOrEmpty(x.ManufacturerDescription));

            // Add "Not Specified" at the top (after "Any") if there are null/empty values
            if (hasNullOrEmptyManufacturer)
            {
                manufacturerOptions.Add(new FilterOption { name = "Not Specified", value = "Not Specified" });
            }

            var manufacturerData = query
                .Where(x => !string.IsNullOrEmpty(x.ManufacturerDescription))
                .GroupBy(x => x.ManufacturerDescription)
                .Select(g => new FilterOption { name = g.Key, value = g.Key, count = g.Count() })
                .OrderBy(x => x.name)
                .ToList();
            manufacturerOptions.AddRange(manufacturerData);
            filterOptions["manufacturerDescription"] = manufacturerOptions;

            return Ok(filterOptions);
        }

        [HttpPost]
        public async Task<IHttpActionResult> GetFilterCountData([FromBody] ConstructionFilterDataDto filterData)
        {
            // Resulting filter count data
            Dictionary<string, Dictionary<string, int>> filterCountData = new Dictionary<string, Dictionary<string, int>>();

            // Current filters applied
            bool anyFiltersApplied = false;
            Dictionary<string, List<string>> currentFilters = new Dictionary<string, List<string>>();

            if (filterData.fields != null)
            {
                foreach (var field in filterData.fields)
                {
                    if (filterData.appliedFilters != null && filterData.appliedFilters[field.field] != null)
                    {
                        List<string> fieldSelections = JsonConvert.DeserializeObject<List<string>>(filterData.appliedFilters[field.field].ToString());
                        bool hasFilter = fieldSelections != null && fieldSelections.Any() && !fieldSelections.Contains("Any");
                        currentFilters[field.field] = hasFilter ? fieldSelections : null;
                        if (!anyFiltersApplied && hasFilter) { anyFiltersApplied = true; }
                    }
                }
            }

            // Generate cache key based on filters and type
            string cacheKey = GenerateConstructionFilterCacheKey(filterData, anyFiltersApplied);

            // Check if we should bypass cache (for admin users)
            bool bypasseCache = SystemParameter.GetIntParm("BypassFilterCountDataCacheAdmin") == 1;

            // Try to get from cache if not bypassing
            if (!bypasseCache)
            {
                // Try to get from the specific cache key first
                filterCountData = (Dictionary<string, Dictionary<string, int>>)CacheHandler.LookupCache(
                    CacheHandler.DataType_MultiFilterCountData, cacheKey);

                if (filterCountData != null)
                {
                    return Ok(filterCountData);
                }

                // If no filters are applied, try the general cache
                if (!anyFiltersApplied)
                {
                    string generalCacheKey = $"ConstructionFilterCountData_{filterData.type?.ToLower() ?? "all"}";
                    filterCountData = (Dictionary<string, Dictionary<string, int>>)CacheHandler.LookupCache(
                        CacheHandler.DataType_MultiFilterCountData, generalCacheKey);

                    if (filterCountData != null)
                    {
                        return Ok(filterCountData);
                    }
                }
            }

            // Reset filter count data for fresh calculation
            filterCountData = new Dictionary<string, Dictionary<string, int>>();

            // Build SQL conditions for current filters
            List<FieldSqlCondition> currentFiltersSqlConditions = GetConstructionSqlConditions(filterData.type);

            // Add current filter conditions
            if (filterData.fields != null)
            {
                foreach (var field in filterData.fields)
                {
                    string fieldName = field.field;
                    string fieldCondition = null;

                    if (currentFilters.ContainsKey(fieldName) && currentFilters[fieldName] != null && currentFilters[fieldName].Any())
                    {
                        var filterValues = currentFilters[fieldName].Where(s => s?.ToLower() != "not specified").ToList();
                        if (filterValues.Any())
                        {
                            var quotedValues = filterValues.Select(v => $"'{v.Replace("'", "''")}'");
                            fieldCondition = $"[{GetColumnNameForField(fieldName)}] IN ({string.Join(",", quotedValues)})";

                            if (currentFilters[fieldName].Any(s => s?.ToLower() == "not specified"))
                            {
                                fieldCondition = $"({fieldCondition} OR [{GetColumnNameForField(fieldName)}] IS NULL)";
                            }
                        }
                        else if (currentFilters[fieldName].Any(s => s?.ToLower() == "not specified"))
                        {
                            fieldCondition = $"[{GetColumnNameForField(fieldName)}] IS NULL";
                        }
                    }

                    if (!string.IsNullOrEmpty(fieldCondition))
                    {
                        currentFiltersSqlConditions.Add(new FieldSqlCondition { fieldName = fieldName, sql = fieldCondition });
                    }
                }
            }

            // Build SQL query using the same pattern as Jobs controller
            string sql = BuildConstructionFilterCountSqlAdvanced(filterData, currentFiltersSqlConditions);

            using (var connection = new SqlConnection(_unitOfWork.Context.Database.Connection.ConnectionString))
            {
                await connection.OpenAsync();
                var cmd = new SqlCommand(sql, connection);

                try
                {
                    using (var sqlReader = await cmd.ExecuteReaderAsync())
                    {
                        // Read each option in each field
                        while (sqlReader.Read())
                        {
                            string fieldName = (string)sqlReader["FieldName"];
                            string optionName = (string)sqlReader["OptionName"];
                            int count = (int)sqlReader["Count"];
                            Dictionary<string, int> fieldCountData = null;
                            if (filterCountData.TryGetValue(fieldName, out fieldCountData))
                                fieldCountData[optionName] = count;
                            else
                                filterCountData[fieldName] = new Dictionary<string, int> { { optionName, count } };
                        }
                        sqlReader.Close();
                    }

                    // Always save to filter-specific cache
                    CacheHandler.SaveToCache(CacheHandler.DataType_MultiFilterCountData, cacheKey, filterCountData);

                    // If no filters applied, also save to the general cache
                    if (!anyFiltersApplied)
                    {
                        string generalCacheKey = $"ConstructionFilterCountData_{filterData.type?.ToLower() ?? "all"}";
                        CacheHandler.SaveToCache(CacheHandler.DataType_MultiFilterCountData, generalCacheKey, filterCountData);
                    }

                    // Make sure next call uses cache
                    if (bypasseCache)
                    {
                        SystemParameter.UpdateIntParm("BypassFilterCountDataCacheAdmin", 0);
                    }

                    return Ok(filterCountData);
                }
                catch (Exception e)
                {
                    throw e;
                }
            }
        }

        // Helper method to generate a cache key based on the applied filters
        private string GenerateConstructionFilterCacheKey(ConstructionFilterDataDto filterData, bool anyFiltersApplied)
        {
            string baseKey = $"ConstructionFilterCountData_{filterData.type?.ToLower() ?? "all"}";

            // If no filters are applied, return the base key
            if (!anyFiltersApplied || filterData.appliedFilters == null)
                return baseKey;

            // Create a hash of the applied filters
            var filterHash = JsonConvert.SerializeObject(filterData.appliedFilters).GetHashCode().ToString("X");
            return $"{baseKey}_{filterHash}";
        }

        // Helper class for SQL conditions
        private class FieldSqlCondition
        {
            public string fieldName { get; set; }
            public string sql { get; set; }
        }

        // Helper method to get base SQL conditions for construction data
        private List<FieldSqlCondition> GetConstructionSqlConditions(string type)
        {
            var conditions = new List<FieldSqlCondition>();

            // Base condition for non-deleted records
            conditions.Add(new FieldSqlCondition { fieldName = "base", sql = "v.Deleted = 0" });

            // Add type filter with same logic as GetListV2
            if (!string.IsNullOrEmpty(type))
            {
                if (type.ToLower() == "construction")
                {
                    // Use same filtering logic as GetListV2 method
                    conditions.Add(new FieldSqlCondition {
                        fieldName = "type",
                        sql = "v.Type = 'surface' AND v.ConstructionCategoryCode NOT IN ('ExteriorDoor', 'InteriorDoor', 'VerticalOpening', 'HorizontalOpening')"
                    });
                }
                else if (type.ToLower() == "opening")
                {
                    // Use same filtering logic as GetListV2 method for openings
                    conditions.Add(new FieldSqlCondition {
                        fieldName = "type",
                        sql = "(v.Type != 'surface' OR v.ConstructionCategoryCode IN ('ExteriorDoor', 'InteriorDoor', 'VerticalOpening', 'HorizontalOpening'))"
                    });
                }
            }

            return conditions;
        }

        // Advanced SQL builder that matches Jobs controller pattern
        private string BuildConstructionFilterCountSqlAdvanced(ConstructionFilterDataDto filterData, List<FieldSqlCondition> currentFiltersSqlConditions)
        {
            if (filterData.fields == null || !filterData.fields.Any())
                return "";

            // Build SQL using UNION pattern like Jobs controller
            var sqlParts = new List<string>();

            foreach (var field in filterData.fields)
            {
                var fieldName = field.field;
                var columnName = GetColumnNameForField(fieldName);

                if (string.IsNullOrEmpty(columnName))
                    continue;

                // Get conditions excluding this field, but include base conditions (deleted, type, etc.)
                var conditions = currentFiltersSqlConditions
                    .Where(c => c.fieldName != fieldName)
                    .Select(c => c.sql)
                    .ToList();

                // Always include base conditions for deleted records and type filtering
                var baseConditions = GetConstructionSqlConditions(filterData.type);
                foreach (var baseCondition in baseConditions)
                {
                    if (!conditions.Any(c => c.Contains(baseCondition.sql)))
                    {
                        conditions.Add(baseCondition.sql);
                    }
                }

                // For insulation field, ensure we only count surface records (excluding doors and openings)
                if (fieldName == "insulationDescription")
                {
                    // Make sure we include the proper surface type condition for insulation (same as GetConstructionSqlConditions)
                    string insulationCondition = "v.Type = 'surface' AND v.ConstructionCategoryCode NOT IN ('ExteriorDoor', 'InteriorDoor', 'VerticalOpening', 'HorizontalOpening')";
                    if (!conditions.Any(c => c.Contains("v.Type = 'surface'")))
                    {
                        conditions.Add(insulationCondition);
                    }
                }

                var whereClause = conditions.Any() ? string.Join(" AND ", conditions) : "1=1";

                // Get all distinct values from database for this field (not just from filterOptions)
                // This ensures we count ALL possible options that exist in the database
                sqlParts.Add($@"
                    SELECT '{fieldName}' [FieldName]
                           ,v.[{columnName}] [OptionName]
                           ,COUNT(*) [Count]
                    FROM [RSS_SurfaceTemplateOpeningTemplateView] v
                    WHERE {whereClause} AND v.[{columnName}] IS NOT NULL AND v.[{columnName}] != ''
                    GROUP BY v.[{columnName}]");

                // Add "Any" count
                sqlParts.Add($@"
                    SELECT '{fieldName}' [FieldName]
                           ,'Any' [OptionName]
                           ,COUNT(*) [Count]
                    FROM [RSS_SurfaceTemplateOpeningTemplateView] v
                    WHERE {whereClause}");

                // Add "Not Specified" count (only for nullable fields)
                // For insulation field, ensure we only count surface records with null values (excluding doors and openings)
                string notSpecifiedWhereClause = whereClause;
                if (fieldName == "insulationDescription")
                {
                    // Make sure we include the proper surface type condition for insulation (same as GetConstructionSqlConditions)
                    string insulationCondition = "v.Type = 'surface' AND v.ConstructionCategoryCode NOT IN ('ExteriorDoor', 'InteriorDoor', 'VerticalOpening', 'HorizontalOpening')";
                    if (!notSpecifiedWhereClause.Contains("v.Type = 'surface'"))
                    {
                        notSpecifiedWhereClause = notSpecifiedWhereClause == "1=1"
                            ? insulationCondition
                            : $"{notSpecifiedWhereClause} AND {insulationCondition}";
                    }
                }

                sqlParts.Add($@"
                    SELECT '{fieldName}' [FieldName]
                           ,'Not Specified' [OptionName]
                           ,COUNT(*) [Count]
                    FROM [RSS_SurfaceTemplateOpeningTemplateView] v
                    WHERE {notSpecifiedWhereClause} AND (v.[{columnName}] IS NULL OR v.[{columnName}] = '')");
            }

            return string.Join(" UNION ", sqlParts);
        }

        // Helper method to build SQL query for filter count data
        private string BuildConstructionFilterCountSql(ConstructionFilterDataDto filterData)
        {
            string baseCondition = "v.Deleted = 0";

            // Add type filter
            if (!string.IsNullOrEmpty(filterData.type))
            {
                if (filterData.type.ToLower() == "construction")
                {
                    baseCondition += " AND v.Type = 'surface'";
                }
                else if (filterData.type.ToLower() == "opening")
                {
                    baseCondition += " AND v.Type = 'opening'";
                }
            }

            // Add applied filters conditions
            if (filterData.appliedFilters != null)
            {
                foreach (var filterField in filterData.appliedFilters.Properties())
                {
                    var fieldName = filterField.Name;
                    var filterValues = filterField.Value.ToObject<List<string>>();

                    if (filterValues != null && filterValues.Any() && !filterValues.Contains("Any"))
                    {
                        string columnName = GetColumnNameForField(fieldName);
                        if (!string.IsNullOrEmpty(columnName))
                        {
                            var quotedValues = filterValues.Select(v => $"'{v.Replace("'", "''")}'");
                            baseCondition += $" AND v.{columnName} IN ({string.Join(",", quotedValues)})";
                        }
                    }
                }
            }

            // Build the SQL query for filter counts
            string sql = $@"
                SELECT 'constructionCategoryTitle' AS FieldName, v.ConstructionCategoryTitle AS OptionName, COUNT(*) AS Count
                FROM RSS_SurfaceTemplateOpeningTemplateView v
                WHERE {baseCondition} AND v.ConstructionCategoryTitle IS NOT NULL
                GROUP BY v.ConstructionCategoryTitle";

            // Add construction sub category counts for construction type
            if (filterData.type?.ToLower() == "construction")
            {
                sql += $@"
                UNION ALL
                SELECT 'constructionSubCategoryTitle' AS FieldName, v.ConstructionSubCategoryTitle AS OptionName, COUNT(*) AS Count
                FROM RSS_SurfaceTemplateOpeningTemplateView v
                WHERE {baseCondition} AND v.ConstructionSubCategoryTitle IS NOT NULL
                GROUP BY v.ConstructionSubCategoryTitle";
            }
            // Add opening style counts for opening type
            else if (filterData.type?.ToLower() == "opening")
            {
                sql += $@"
                UNION ALL
                SELECT 'openingStyleTitle' AS FieldName, v.OpeningStyleTitle AS OptionName, COUNT(*) AS Count
                FROM RSS_SurfaceTemplateOpeningTemplateView v
                WHERE {baseCondition} AND v.OpeningStyleTitle IS NOT NULL
                GROUP BY v.OpeningStyleTitle";
            }

            return sql;
        }

        // Helper method to get column name for filter field
        private string GetColumnNameForField(string fieldName)
        {
            switch (fieldName)
            {
                case "constructionCategoryTitle":
                    return "ConstructionCategoryTitle";
                case "constructionSubCategoryTitle":
                    return "ConstructionSubCategoryTitle";
                case "openingStyleTitle":
                    return "OpeningStyleTitle";
                case "insulationDescription":
                    return "InsulationDescription";
                case "manufacturerDescription":
                    return "ManufacturerDescription";
                case "glassTypeTitle":
                    return "GlassTypeTitle";
                case "glassColourTitle":
                    return "GlassColourTitle";
                case "lowECoating":
                    return "LowECoating";
                case "frameMaterialTitle":
                    return "FrameMaterialTitle";
                default:
                    return null;
            }
        }

        private IQueryable GetConstructionQuery(string type)
        {
            type = type.ToLower();

            if (type == "construction")
            {
                return _unitOfWork.Context.RSS_SurfaceTemplate
                    .Where(x => !x.Deleted);
            }
            else if (type == "opening")
            {
                return _unitOfWork.Context.RSS_OpeningTemplate
                    .Where(x => !x.Deleted);
            }

            return null;
        }

        private IQueryable<RSS_SurfaceTemplate> AlterQueryFromMultiFiltersForSurface(IQueryable<RSS_SurfaceTemplate> query, ConstructionFilterDataDto filterData)
        {
            if (filterData.appliedFilters == null)
                return query;

            foreach (var filterField in filterData.appliedFilters.Properties())
            {
                var fieldName = filterField.Name;
                var filterValues = filterField.Value.ToObject<List<string>>();

                // Skip if no values or if "Any" is selected (following JobController pattern)
                if (filterValues == null || !filterValues.Any() ||
                    filterValues.Any(x => x?.ToLower() == "any"))
                    continue;

                switch (fieldName)
                {
                    case "constructionCategoryTitle":
                        query = query.Where(x => filterValues.Contains(x.RSS_ConstructionCategory.Title));
                        break;
                    case "constructionSubCategoryTitle":
                        query = query.Where(x => filterValues.Contains(x.RSS_ConstructionSubCategory.Title));
                        break;
                }
            }

            return query;
        }

        private IQueryable<RSS_OpeningTemplate> AlterQueryFromMultiFiltersForOpening(IQueryable<RSS_OpeningTemplate> query, ConstructionFilterDataDto filterData)
        {
            if (filterData.appliedFilters == null)
                return query;

            foreach (var filterField in filterData.appliedFilters.Properties())
            {
                var fieldName = filterField.Name;
                var filterValues = filterField.Value.ToObject<List<string>>();

                // Skip if no values or if "Any" is selected (following JobController pattern)
                if (filterValues == null || !filterValues.Any() ||
                    filterValues.Any(x => x?.ToLower() == "any"))
                    continue;

                switch (fieldName)
                {
                    case "constructionCategoryTitle":
                        query = query.Where(x => filterValues.Contains(x.RSS_ConstructionCategory.Title));
                        break;
                    case "openingStyleTitle":
                        query = query.Where(x => filterValues.Contains(x.RSS_OpeningStyle.Title));
                        break;
                }
            }

            return query;
        }

        private IQueryable<RSS_SurfaceTemplateOpeningTemplateView> AlterQueryFromMultiFiltersForView(IQueryable<RSS_SurfaceTemplateOpeningTemplateView> query, ConstructionFilterDataDto filterData)
        {
            if (filterData.appliedFilters == null)
                return query;

            foreach (var filterField in filterData.appliedFilters.Properties())
            {
                var fieldName = filterField.Name;
                var filterValues = filterField.Value.ToObject<List<string>>();

                // Skip if no values or if "Any" is selected (following JobController pattern)
                if (filterValues == null || !filterValues.Any() ||
                    filterValues.Any(x => x?.ToLower() == "any"))
                    continue;

                switch (fieldName)
                {
                    case "constructionCategoryTitle":
                        query = query.Where(x => filterValues.Contains(x.ConstructionCategoryTitle));
                        break;
                    case "constructionSubCategoryTitle":
                        query = query.Where(x => filterValues.Contains(x.ConstructionSubCategoryTitle));
                        break;
                    case "openingStyleTitle":
                        query = query.Where(x => filterValues.Contains(x.OpeningStyleTitle));
                        break;
                    case "manufacturerDescription":
                        // Handle "Not Specified" values like other nullable fields
                        var nonNotSpecifiedManufacturerValues = filterValues.Where(v => v?.ToLower() != "not specified").ToList();
                        var hasNotSpecifiedManufacturer = filterValues.Any(v => v?.ToLower() == "not specified");

                        if (nonNotSpecifiedManufacturerValues.Any() && hasNotSpecifiedManufacturer)
                        {
                            // Both specific values and "Not Specified" are selected
                            query = query.Where(x => nonNotSpecifiedManufacturerValues.Contains(x.ManufacturerDescription) ||
                                                   string.IsNullOrEmpty(x.ManufacturerDescription));
                        }
                        else if (nonNotSpecifiedManufacturerValues.Any())
                        {
                            // Only specific values are selected
                            query = query.Where(x => nonNotSpecifiedManufacturerValues.Contains(x.ManufacturerDescription));
                        }
                        else if (hasNotSpecifiedManufacturer)
                        {
                            // Only "Not Specified" is selected
                            query = query.Where(x => string.IsNullOrEmpty(x.ManufacturerDescription));
                        }
                        break;
                    case "description":
                        query = query.Where(x => filterValues.Contains(x.Description));
                        break;
                    case "insulationDescription":
                        // Handle "Not Specified" values like other nullable fields
                        var nonNotSpecifiedValues = filterValues.Where(v => v?.ToLower() != "not specified").ToList();
                        var hasNotSpecified = filterValues.Any(v => v?.ToLower() == "not specified");

                        if (nonNotSpecifiedValues.Any() && hasNotSpecified)
                        {
                            // Both specific values and "Not Specified" are selected
                            // Apply construction type restrictions for insulation field
                            query = query.Where(x => (nonNotSpecifiedValues.Contains(x.InsulationDescription) ||
                                                     string.IsNullOrEmpty(x.InsulationDescription)) &&
                                                   x.Type == "surface" &&
                                                   x.ConstructionCategoryCode != "ExteriorDoor" &&
                                                   x.ConstructionCategoryCode != "InteriorDoor" &&
                                                   x.ConstructionCategoryCode != "VerticalOpening" &&
                                                   x.ConstructionCategoryCode != "HorizontalOpening");
                        }
                        else if (nonNotSpecifiedValues.Any())
                        {
                            // Only specific values are selected
                            // Apply construction type restrictions for insulation field
                            query = query.Where(x => nonNotSpecifiedValues.Contains(x.InsulationDescription) &&
                                               x.Type == "surface" &&
                                               x.ConstructionCategoryCode != "ExteriorDoor" &&
                                               x.ConstructionCategoryCode != "InteriorDoor" &&
                                               x.ConstructionCategoryCode != "VerticalOpening" &&
                                               x.ConstructionCategoryCode != "HorizontalOpening");
                        }
                        else if (hasNotSpecified)
                        {
                            // Only "Not Specified" is selected
                            // Apply construction type restrictions for insulation field
                            query = query.Where(x => string.IsNullOrEmpty(x.InsulationDescription) &&
                                               x.Type == "surface" &&
                                               x.ConstructionCategoryCode != "ExteriorDoor" &&
                                               x.ConstructionCategoryCode != "InteriorDoor" &&
                                               x.ConstructionCategoryCode != "VerticalOpening" &&
                                               x.ConstructionCategoryCode != "HorizontalOpening");
                        }
                        break;
                    case "glassTypeTitle":
                        // Handle "Not Specified" values like other nullable fields
                        var nonNotSpecifiedGlassTypeValues = filterValues.Where(v => v?.ToLower() != "not specified").ToList();
                        var hasNotSpecifiedGlassType = filterValues.Any(v => v?.ToLower() == "not specified");

                        if (nonNotSpecifiedGlassTypeValues.Any() && hasNotSpecifiedGlassType)
                        {
                            // Both specific values and "Not Specified" are selected
                            query = query.Where(x => nonNotSpecifiedGlassTypeValues.Contains(x.GlassTypeTitle) ||
                                                   string.IsNullOrEmpty(x.GlassTypeTitle));
                        }
                        else if (nonNotSpecifiedGlassTypeValues.Any())
                        {
                            // Only specific values are selected
                            query = query.Where(x => nonNotSpecifiedGlassTypeValues.Contains(x.GlassTypeTitle));
                        }
                        else if (hasNotSpecifiedGlassType)
                        {
                            // Only "Not Specified" is selected
                            query = query.Where(x => string.IsNullOrEmpty(x.GlassTypeTitle));
                        }
                        break;
                    case "glassColourTitle":
                        // Handle "Not Specified" values like other nullable fields
                        var nonNotSpecifiedGlassColourValues = filterValues.Where(v => v?.ToLower() != "not specified").ToList();
                        var hasNotSpecifiedGlassColour = filterValues.Any(v => v?.ToLower() == "not specified");

                        if (nonNotSpecifiedGlassColourValues.Any() && hasNotSpecifiedGlassColour)
                        {
                            // Both specific values and "Not Specified" are selected
                            query = query.Where(x => nonNotSpecifiedGlassColourValues.Contains(x.GlassColourTitle) ||
                                                   string.IsNullOrEmpty(x.GlassColourTitle));
                        }
                        else if (nonNotSpecifiedGlassColourValues.Any())
                        {
                            // Only specific values are selected
                            query = query.Where(x => nonNotSpecifiedGlassColourValues.Contains(x.GlassColourTitle));
                        }
                        else if (hasNotSpecifiedGlassColour)
                        {
                            // Only "Not Specified" is selected
                            query = query.Where(x => string.IsNullOrEmpty(x.GlassColourTitle));
                        }
                        break;
                    case "frameMaterialTitle":
                        // Handle "Not Specified" values like other nullable fields
                        var nonNotSpecifiedFrameMaterialValues = filterValues.Where(v => v?.ToLower() != "not specified").ToList();
                        var hasNotSpecifiedFrameMaterial = filterValues.Any(v => v?.ToLower() == "not specified");

                        if (nonNotSpecifiedFrameMaterialValues.Any() && hasNotSpecifiedFrameMaterial)
                        {
                            // Both specific values and "Not Specified" are selected
                            query = query.Where(x => nonNotSpecifiedFrameMaterialValues.Contains(x.FrameMaterialTitle) ||
                                                   string.IsNullOrEmpty(x.FrameMaterialTitle));
                        }
                        else if (nonNotSpecifiedFrameMaterialValues.Any())
                        {
                            // Only specific values are selected
                            query = query.Where(x => nonNotSpecifiedFrameMaterialValues.Contains(x.FrameMaterialTitle));
                        }
                        else if (hasNotSpecifiedFrameMaterial)
                        {
                            // Only "Not Specified" is selected
                            query = query.Where(x => string.IsNullOrEmpty(x.FrameMaterialTitle));
                        }
                        break;
                    case "lowECoating":
                        // Handle "Not Specified" values like other nullable fields
                        var nonNotSpecifiedLowEValues = filterValues.Where(v => v?.ToLower() != "not specified").ToList();
                        var hasNotSpecifiedLowE = filterValues.Any(v => v?.ToLower() == "not specified");

                        if (nonNotSpecifiedLowEValues.Any() && hasNotSpecifiedLowE)
                        {
                            // Both specific values and "Not Specified" are selected
                            query = query.Where(x => nonNotSpecifiedLowEValues.Contains(x.LowECoating) ||
                                                   string.IsNullOrEmpty(x.LowECoating));
                        }
                        else if (nonNotSpecifiedLowEValues.Any())
                        {
                            // Only specific values are selected
                            query = query.Where(x => nonNotSpecifiedLowEValues.Contains(x.LowECoating));
                        }
                        else if (hasNotSpecifiedLowE)
                        {
                            // Only "Not Specified" is selected
                            query = query.Where(x => string.IsNullOrEmpty(x.LowECoating));
                        }
                        break;
                }
            }

            return query;
        }

        /// <summary>
        /// Bulk returns all construction views with no pagination (Used for front-end filtering).
        /// </summary>
        public IHttpActionResult GetAll()
        {
            var s = _constructionContext.GetAll();
            return Ok(s);
        }

        [Authorize(Roles = "settings__settings__create")]
        [HttpPost]
        public IHttpActionResult CreateSurface([FromBody] SurfaceTemplateDto constructionDto)
        {
            var guid = _constructionContext.CreateSurface(constructionDto);

            // Clear Construction Database caches
            ClearConstructionCaches();

            return Ok(guid);
        }

        [Authorize(Roles = "settings__settings__edit")]
        [HttpPost]
        public IHttpActionResult UpdateSurface([FromBody] SurfaceTemplateDto surfaceDto)
        {
            _constructionContext.UpdateSurface(surfaceDto);

            // Clear Construction Database caches
            ClearConstructionCaches();

            return Ok();
        }

        [Authorize(Roles = "settings__settings__create")]
        [HttpPost]
        public IHttpActionResult CreateOpening([FromBody] OpeningTemplateDto constructionDto)
        {
            var guid = _constructionContext.CreateOpening(constructionDto);

            // Clear Construction Database caches
            ClearConstructionCaches();

            return Ok(guid);
        }

        [Authorize(Roles = "settings__settings__edit")]
        [HttpPost]
        public IHttpActionResult UpdateOpening([FromBody] OpeningTemplateDto constructionDto)
        {
            _constructionContext.UpdateOpening(constructionDto);

            // Clear Construction Database caches
            ClearConstructionCaches();

            return Ok();
        }

        [Authorize(Roles = "settings__settings__edit")]
        [HttpPost]
        public IHttpActionResult SetSurfaceIsFavourite([FromUri] Guid constructionId, [FromUri] bool isFavourite)
        {
            _constructionContext.SetSurfaceIsFavourite(constructionId, isFavourite);

            // Clear Construction Database caches
            ClearConstructionCaches();

            return Ok();
        }

        [Authorize(Roles = "settings__settings__edit")]
        [HttpPost]
        public IHttpActionResult SetOpeningIsFavourite([FromUri] Guid constructionId, [FromUri] bool isFavourite)
        {
            _constructionContext.SetOpeningIsFavourite(constructionId, isFavourite);

            // Clear Construction Database caches
            ClearConstructionCaches();

            return Ok();
        }

        [Authorize(Roles = "settings__settings__delete")]
        [HttpPost]
        public IHttpActionResult Delete([FromUri] Guid constructionId, [FromUri] string type)
        {

            type = type.ToLower();

            if (type == "surface")
            {
                _constructionContext.Delete<SurfaceTemplateDto>(constructionId);

                // Clear Construction Database caches
                ClearConstructionCaches();

                return Ok();
            }
            if (type == "opening")
            {
                _constructionContext.Delete<OpeningTemplateDto>(constructionId);

                // Clear Construction Database caches
                ClearConstructionCaches();

                return Ok();
            }
            else
                throw new NotImplementedException("Incorrect 'type' supplied!");

        }

        [Authorize(Roles = "settings__settings__delete")]
        [HttpPost]
        public IHttpActionResult UndoDelete([FromUri] Guid constructionId, [FromUri] string type)
        {
            type = type.ToLower();

            if (type == "surface")
            {
                _constructionContext.UndoDelete<SurfaceTemplateDto>(constructionId);

                // Clear Construction Database caches
                ClearConstructionCaches();

                return Ok();
            }
            if (type == "opening")
            {
                _constructionContext.UndoDelete<OpeningTemplateDto>(constructionId);

                // Clear Construction Database caches
                ClearConstructionCaches();

                return Ok();
            }
            else
                throw new NotImplementedException("Incorrect 'type' supplied!");
        }

        [Authorize(Roles = "settings__settings__create")]
        [HttpPost]
        public IHttpActionResult Copy([FromUri] Guid constructionId, [FromUri] string type)
        {

            type = type.ToLower();

            if (type == "surface")
            {
                var id = _constructionContext.Copy<SurfaceTemplateDto>(constructionId);

                // Clear Construction Database caches
                ClearConstructionCaches();

                return Ok(id);
            }
            if (type == "opening")
            {
                var id = _constructionContext.Copy<OpeningTemplateDto>(constructionId);

                // Clear Construction Database caches
                ClearConstructionCaches();

                return Ok(id);
            }
            else
                throw new NotImplementedException("Incorrect 'type' supplied!");

        }

        [HttpGet]
        public IHttpActionResult GetConstructionCategoryList()
        {
            return Ok(_constructionContext.GetConstructionCategoryList());
        }

        [HttpGet]
        public IHttpActionResult GetConstructionSubCategoryList()
        {
            return Ok(_constructionContext.GetConstructionSubCategoryList());
        }

        [HttpGet]
        public IHttpActionResult GetUnitOfMeasureList()
        {
            var list = _unitOfWork.Context.RSS_UnitOfMeasure
                .Where(a => a.Deleted == false)
                .OrderBy(a => a.SortOrder)
                .ProjectTo<UnitOfMeasureDto>(_mapper.ConfigurationProvider);

            return Ok(list);
        }

        [HttpGet]
        public IHttpActionResult GetAdjacencyList()
        {
            var list = _unitOfWork.Context.RSS_Adjacency
                .Where(a => a.Deleted == false)
                .OrderBy(a => a.SortOrder)
                .ProjectTo<AdjacencyDto>(_mapper.ConfigurationProvider);

            return Ok(list);
        }

        [HttpGet]
        public IHttpActionResult GetAirCavityList()
        {
            var list = _unitOfWork.Context.RSS_AirCavity
                .Where(a => a.Deleted == false)
                .OrderBy(a => a.SortOrder)
                .ProjectTo<AirCavityDto>(_mapper.ConfigurationProvider);

            return Ok(list);
        }

        [HttpGet]
        public IHttpActionResult GetOpeningStyleList()
        {
            var list = _unitOfWork.Context.RSS_OpeningStyle
                .Where(a => a.Deleted == false)
                .OrderBy(a => a.SortOrder)
                .ProjectTo<OpeningStyleDto>(_mapper.ConfigurationProvider);

            return Ok(list);
        }

        [HttpGet]
        public IHttpActionResult GetNccOpeningStyleList()
        {
            var list = _unitOfWork.Context.RSS_NccOpeningStyle
                .Where(a => a.Deleted == false)
                .OrderBy(a => a.SortOrder)
                .ProjectTo<NccOpeningStyleDto>(_mapper.ConfigurationProvider);

            return Ok(list);
        }

        [HttpGet]
        public IHttpActionResult GetFrameMaterialList()
        {
            var list = _unitOfWork.Context.RSS_FrameMaterial
                .Where(a => a.Deleted == false)
                .OrderBy(a => a.SortOrder)
                .ProjectTo<FrameMaterialDto>(_mapper.ConfigurationProvider);

            return Ok(list);
        }

        [HttpGet]
        public IHttpActionResult GetGlassTypeList()
        {
            var list = _unitOfWork.Context.RSS_GlassType
                .Where(a => a.Deleted == false)
                .OrderBy(a => a.SortOrder)
                .ProjectTo<GlassTypeDto>(_mapper.ConfigurationProvider);

            return Ok(list);
        }

        [HttpGet]
        public IHttpActionResult GetGlassColourList()
        {
            var list = _unitOfWork.Context.RSS_GlassColour
                .Where(a => a.Deleted == false)
                .OrderBy(a => a.SortOrder)
                .ProjectTo<GlassColourDto>(_mapper.ConfigurationProvider);

            return Ok(list);
        }

        /// <summary>
        /// Exports the construction database to an Excel file in the same format as expected for imports
        /// </summary>
        /// <param name="type">Specify "construction" or "opening" - anything else will fail.</param>
        /// <param name="ids">Optional list of construction IDs to export. If not provided, all non-deleted constructions will be exported.</param>
        /// <returns>Excel file as a response</returns>
        [HttpPost]
        public HttpResponseMessage ExportConstructionDatabase(string type, [FromBody] List<Guid> ids)
        {
            type = type.ToLower();
            if (type != "construction" && type != "opening")
                throw new HttpResponseException(System.Net.HttpStatusCode.BadRequest);

            try
            {
                // Create a new Excel package
                using (var package = new OfficeOpenXml.ExcelPackage())
                {
                    // Get the data based on the type
                    if (type == "construction")
                    {
                        // Create worksheets for each construction category with optional filtering by IDs
                        CreateConstructionWorksheets(package, ids);
                    }
                    else if (type == "opening")
                    {
                        // Create worksheets for each opening category with optional filtering by IDs
                        CreateOpeningWorksheets(package, ids);
                    }

                    // Return the Excel file
                    var response = new HttpResponseMessage(System.Net.HttpStatusCode.OK)
                    {
                        Content = new ByteArrayContent(package.GetAsByteArray())
                    };
                    response.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                    response.Content.Headers.ContentDisposition = new System.Net.Http.Headers.ContentDispositionHeaderValue("attachment")
                    {
                        FileName = type + "Database.xlsx"
                    };
                    return response;
                }
            }
            catch (Exception ex)
            {
                throw new HttpResponseException(System.Net.HttpStatusCode.InternalServerError);
            }
        }

        private void CreateConstructionWorksheets(OfficeOpenXml.ExcelPackage package, List<Guid> ids)
        {
            // Get all construction categories
            var categories = _constructionContext.GetConstructionCategoryList();

            // Get construction categories (surfaces excluding doors)
            var constructionCategories = categories
                .Where(c => c.Type == "surface" &&
                       c.ConstructionCategoryCode != "ExteriorDoor" &&
                       c.ConstructionCategoryCode != "InteriorDoor")
                .ToList();

            // Get surface templates based on provided IDs or all if no IDs provided
            var query = _unitOfWork.Context.RSS_SurfaceTemplate.Where(s => !s.Deleted);

            // Filter by IDs if provided
            if (ids != null && ids.Any())
            {
                query = query.Where(s => ids.Contains(s.ConstructionId));
            }

            var surfaces = query.ProjectTo<SurfaceTemplateDto>(_mapper.ConfigurationProvider).ToList();

            // Create a dictionary to store surfaces grouped by category code
            var surfacesByCategory = new Dictionary<string, List<SurfaceTemplateDto>>();

            // Group surfaces by category code and create worksheets
            foreach (var category in constructionCategories)
            {
                string categoryCode = category.ConstructionCategoryCode;

                // Get surfaces for this category
                surfacesByCategory[categoryCode] = surfaces.Where(s => s.Category?.ConstructionCategoryCode == categoryCode).ToList();

                // Create worksheet for this category if it contains surfaces
                if (surfacesByCategory[categoryCode].Any())
                {
                    // Use the category title from the database
                    string worksheetName = category.Title ?? categoryCode;
                    CreateSurfaceWorksheet(package, worksheetName, surfacesByCategory[categoryCode]);
                }
            }
        }

        private void CreateOpeningWorksheets(OfficeOpenXml.ExcelPackage package, List<Guid> ids)
        {
            // Get surface templates for doors based on provided IDs or all if no IDs provided
            var surfacesQuery = _unitOfWork.Context.RSS_SurfaceTemplate
                .Where(s => !s.Deleted &&
                      (s.ConstructionCategoryCode == "ExteriorDoor" ||
                       s.ConstructionCategoryCode == "InteriorDoor"));

            // Filter by IDs if provided
            if (ids != null && ids.Any())
            {
                surfacesQuery = surfacesQuery.Where(s => ids.Contains(s.ConstructionId));
            }

            var surfaces = surfacesQuery.ProjectTo<SurfaceTemplateDto>(_mapper.ConfigurationProvider)
                .ToList();

            // Get opening templates based on provided IDs or all if no IDs provided
            var openingsQuery = _unitOfWork.Context.RSS_OpeningTemplate.Where(o => !o.Deleted);

            // Filter by IDs if provided
            if (ids != null && ids.Any())
            {
                openingsQuery = openingsQuery.Where(o => ids.Contains(o.ConstructionId));
            }

            var openings = openingsQuery.ProjectTo<OpeningTemplateDto>(_mapper.ConfigurationProvider)
                .ToList();

            // Group by category
            var exteriorDoors = surfaces.Where(s => s.Category?.ConstructionCategoryCode == "ExteriorDoor").ToList();
            var interiorDoors = surfaces.Where(s => s.Category?.ConstructionCategoryCode == "InteriorDoor").ToList();
            var exteriorGlazing = openings.Where(o => o.Category?.ConstructionCategoryCode == "ExteriorGlazing").ToList();
            var skylights = openings.Where(o => o.Category?.ConstructionCategoryCode == "Skylight").ToList();
            var roofWindows = openings.Where(o => o.Category?.ConstructionCategoryCode == "RoofWindow").ToList();

            // Create worksheets for each category
            CreateSurfaceWorksheet(package, "Exterior Door", exteriorDoors);
            CreateSurfaceWorksheet(package, "Interior Door", interiorDoors);
            CreateOpeningWorksheet(package, "Exterior Glazing", exteriorGlazing);
            CreateOpeningWorksheet(package, "Skylight", skylights);
            CreateOpeningWorksheet(package, "Roof Window", roofWindows);
        }

        /// <summary>
        /// Gets the field definitions for a surface category
        /// </summary>
        private List<(string Header, Func<SurfaceTemplateDto, object> ValueGetter)> GetSurfaceFieldDefinitions(string categoryCode)
        {
            // Common fields for all surface categories - in order they appear in the UI
            var fields = new List<(string Header, Func<SurfaceTemplateDto, object> ValueGetter)>();

            // General section
            Func<SurfaceTemplateDto, string> getCategory = s => s.Category?.Title;
            fields.Add(("Category", getCategory));

            // Add Construction Type field for all surface categories except doors
            if (categoryCode != "ExteriorDoor" && categoryCode != "InteriorDoor")
            {
                Func<SurfaceTemplateDto, string> getConstructionType = s => s.SubCategory?.Title;
                fields.Add(("Construction Type", getConstructionType));
            }

            Func<SurfaceTemplateDto, string> getDescription = s => s.Description;
            fields.Add(("Description", getDescription));

            Func<SurfaceTemplateDto, string> getDisplayDescription = s => s.Description;
            fields.Add(("Display Description", getDisplayDescription));

            Func<SurfaceTemplateDto, string> getShowInReport = s => s.ShowInReport ? "Yes" : "No";
            fields.Add(("Show in Report", getShowInReport));

            Func<SurfaceTemplateDto, string> getManufacturer = s => s.Manufacturer?.Description;
            fields.Add(("Manufacturer", getManufacturer));

            Func<SurfaceTemplateDto, string> getConstructionId = s => s.ExternalConstructionId;
            fields.Add(("Construction ID", getConstructionId));

            // Add Adjacency field for all categories
            Func<SurfaceTemplateDto, string> getAdjacency = s => s.Adjacency?.Title;
            fields.Add(("Adjacency", getAdjacency));

            Func<SurfaceTemplateDto, string> getUnitOfMeasurement = s => s.UnitOfMeasure?.Title;
            fields.Add(("Unit of Measurement", getUnitOfMeasurement));

            // Add Favourite field
            Func<SurfaceTemplateDto, string> getFavourite = s => s.IsFavourite ? "Yes" : "No";
            fields.Add(("Favourite", getFavourite));

            // Add physical properties
            Func<SurfaceTemplateDto, object> getThickness = s => s.Thickness;
            fields.Add(("Thickness (mm)", getThickness));

            Func<SurfaceTemplateDto, object> getDensity = s => s.Density;
            fields.Add(("Density (kg/m²)", getDensity));

            Func<SurfaceTemplateDto, object> getTilt = s => s.Tilt;
            fields.Add(("Tilt (°)", getTilt));

            // Add Floor Covering for floor categories (any category with "floor" in the title but not "subfloor")
            if (categoryCode.ToLower().Contains("floor") && !categoryCode.ToLower().Contains("subfloor"))
            {
                Func<SurfaceTemplateDto, string> getFloorCovering = s => s.FloorCovering;
                fields.Add(("Floor Covering", getFloorCovering));
            }

            // Add color and absorptance fields
            Func<SurfaceTemplateDto, string> getExteriorColour = s => s.ExteriorColour?.Title;
            fields.Add(("Exterior Colour", getExteriorColour));

            Func<SurfaceTemplateDto, object> getExteriorSolarAbsorptance = s => s.ExteriorSolarAbsorptance;
            fields.Add(("Exterior Solar Absorptance", getExteriorSolarAbsorptance));

            Func<SurfaceTemplateDto, string> getInteriorColour = s => s.InteriorColour?.Title;
            fields.Add(("Interior Colour", getInteriorColour));

            Func<SurfaceTemplateDto, object> getInteriorSolarAbsorptance = s => s.InteriorSolarAbsorptance;
            fields.Add(("Interior Solar Absorptance", getInteriorSolarAbsorptance));

            // Add Air Cavity field
            Func<SurfaceTemplateDto, string> getAirCavity = s => s.AirCavity?.Title;
            fields.Add(("Air Cavity", getAirCavity));



            // Add Thermal Bridge field for categories that require it
            var category = _constructionContext.GetConstructionCategoryList()
                .FirstOrDefault(c => c.ConstructionCategoryCode == categoryCode);

            if (category?.RequiresThermalBridge == true)
            {
                Func<SurfaceTemplateDto, string> getThermalBridge = s => (s.ThermalBridge ?? false) ? "Yes" : "No";
                fields.Add(("Thermal Bridge", getThermalBridge));
            }

            // Add Full Masonry field for categories that require it
            if (category?.RequiresIsFullMasonry == true)
            {
                Func<SurfaceTemplateDto, string> getFullMasonry = s => (s.IsFullMasonry ?? false) ? "Yes" : "No";
                fields.Add(("Full Masonry", getFullMasonry));
            }

            // Add System R-Value field
            Func<SurfaceTemplateDto, object> getSystemRValue = s => s.SystemRValue;
            fields.Add(("System R-Value", getSystemRValue));

            // Add door-specific fields
            if (categoryCode == "ExteriorDoor" || categoryCode == "InteriorDoor")
            {
                Func<SurfaceTemplateDto, string> getOpeningStyle = s => s.OpeningStyle?.Title;
                fields.Add(("Opening Style", getOpeningStyle));

                Func<SurfaceTemplateDto, string> getNccOpeningStyle = s => s.NccOpeningStyle?.Title;
                fields.Add(("NCC Opening Style", getNccOpeningStyle));

                Func<SurfaceTemplateDto, string> getWeatherStripped = s => (s.HasWeatherStrip ?? false) ? "Yes" : "No";
                fields.Add(("Weather Stripped", getWeatherStripped));

                Func<SurfaceTemplateDto, string> getInsectScreen = s => (s.HasInsectScreen ?? false) ? "Yes" : "No";
                fields.Add(("Insect Screen", getInsectScreen));
            }

            // Add Insulation section
            Func<SurfaceTemplateDto, string> getInsulation = s => (s.InsulationData?.HasData ?? false) ? "Yes" : "No";
            fields.Add(("Insulation", getInsulation));

            // Add insulation child fields for all categories except doors
            if (categoryCode != "ExteriorDoor" && categoryCode != "InteriorDoor")
            {
                // Only include insulation details if Insulation is Yes
                // Use override insulation description if it exists, otherwise fall back to original
                Func<SurfaceTemplateDto, string> getInsulationDescription = s =>
                    (s.InsulationData?.HasData ?? false) ?
                        (!string.IsNullOrEmpty(s.OverrideInsulationDescription) ? s.OverrideInsulationDescription : s.InsulationData?.Description) : "";
                fields.Add(("Insulation Description", getInsulationDescription));

                // Insulation R-Value
                Func<SurfaceTemplateDto, object> getInsulationRValue = s =>
                    (s.InsulationData?.HasData ?? false) ? s.InsulationData?.RValue : null;
                fields.Add(("Insulation R-Value", getInsulationRValue));

                // Add Perimeter Insulation for categories that can have it
                if (category?.CanHavePerimeterInsulation == true)
                {
                    Func<SurfaceTemplateDto, string> getPerimeterInsulation = s =>
                        (s.InsulationData?.HasData ?? false) ?
                        ((s.InsulationData?.HasSlabEdgeInsulation ?? false) ? "Yes" : "No") : "";
                    fields.Add(("Perimeter Insulation", getPerimeterInsulation));

                    Func<SurfaceTemplateDto, object> getPerimeterRValue = s =>
                        (s.InsulationData?.HasData ?? false) && (s.InsulationData?.HasSlabEdgeInsulation ?? false) ?
                        s.InsulationData?.SlabEdgeInsulationRValue : null;
                    fields.Add(("Perimeter Insulation R-Value", getPerimeterRValue));
                }

                // Bulk and Reflective Insulation
                Func<SurfaceTemplateDto, string> getBulkInsulation = s =>
                    (s.InsulationData?.HasData ?? false) ?
                    ((s.InsulationData?.IsBulk ?? false) ? "Yes" : "No") : "";
                fields.Add(("Bulk Insulation", getBulkInsulation));

                Func<SurfaceTemplateDto, string> getReflectiveInsulation = s =>
                    (s.InsulationData?.HasData ?? false) ?
                    ((s.InsulationData?.IsReflective ?? false) ? "Yes" : "No") : "";
                fields.Add(("Reflective Insulation", getReflectiveInsulation));
            }

            // Add Life Cycle and Cost section
            Func<SurfaceTemplateDto, string> getLifeCycleAndCost = s => (s.LifeCycleData?.HasConstructionLifeCycleAndCost ?? false) ? "Yes" : "No";
            fields.Add(("Life Cycle and Cost", getLifeCycleAndCost));

            // Add lifecycle child fields for all categories
            Func<SurfaceTemplateDto, object> getCost = s =>
                (s.LifeCycleData?.HasConstructionLifeCycleAndCost ?? false) ? s.LifeCycleData?.CostPerM2 : null;
            fields.Add(("Cost ($)", getCost));

            Func<SurfaceTemplateDto, object> getEmbodiedEnergy = s =>
                (s.LifeCycleData?.HasConstructionLifeCycleAndCost ?? false) ? s.LifeCycleData?.EmbodiedEnergy : null;
            fields.Add(("Embodied Energy (MJ)", getEmbodiedEnergy));

            Func<SurfaceTemplateDto, object> getEmbodiedWater = s =>
                (s.LifeCycleData?.HasConstructionLifeCycleAndCost ?? false) ? s.LifeCycleData?.EmbodiedWater : null;
            fields.Add(("Embodied Water (L)", getEmbodiedWater));

            Func<SurfaceTemplateDto, object> getEmbodiedGHG = s =>
                (s.LifeCycleData?.HasConstructionLifeCycleAndCost ?? false) ? s.LifeCycleData?.EmbodiedGhgEmissions : null;
            fields.Add(("Embodied GHG Emissions (kgCO₂e)", getEmbodiedGHG));

            // Add Chenath Scratch File section
            Func<SurfaceTemplateDto, string> getChenathScratchFile = s => (s.ChenathData?.HasData ?? false) ? "Yes" : "No";
            fields.Add(("Chenath Scratch File", getChenathScratchFile));

            // Add Chenath child fields
            Func<SurfaceTemplateDto, string> getConstructionDescription = s =>
                (s.ChenathData?.HasData ?? false) ? s.ChenathData?.ConstructionDescription : "";
            fields.Add(("Construction Description", getConstructionDescription));

            Func<SurfaceTemplateDto, string> getConstructionCodeRange = s =>
                (s.ChenathData?.HasData ?? false) ? (s.ChenathData?.ConstructionTypeCodeRange != null ?
                    $"{s.ChenathData?.ConstructionTypeCodeRange?.Min} - {s.ChenathData?.ConstructionTypeCodeRange?.Max}" : "") : "";
            fields.Add(("Chenath Construction Code Range", getConstructionCodeRange));

            // Add construction code per software
            var assessmentSoftwareList = _unitOfWork.Context.RSS_AssessmentSoftware
                .Where(a => !a.Deleted)
                .OrderBy(a => a.Description)
                .ToList();

            foreach (var software in assessmentSoftwareList)
            {
                string softwareCode = software.AssessmentSoftwareCode.ToLower();
                string softwareName = software.Description;

                Func<SurfaceTemplateDto, string> getConstructionCodeForSoftware = s =>
                    (s.ChenathData?.HasData ?? false) &&
                    (s.ChenathData?.ConstructionCodePerSoftware?.ContainsKey(softwareCode) ?? false) ?
                    s.ChenathData?.ConstructionCodePerSoftware[softwareCode] : "";

                fields.Add(($"{softwareName} Construction Code", getConstructionCodeForSoftware));
            }

            // Add EnergyPlus IDF File section
            Func<SurfaceTemplateDto, string> getEnergyPlusIdfFile = s => (s.EPData?.HasData ?? false) ? "Yes" : "No";
            fields.Add(("EnergyPlus IDF File", getEnergyPlusIdfFile));

            // Add EnergyPlus child fields
            Func<SurfaceTemplateDto, string> getEnergyPlusName = s =>
                (s.EPData?.HasData ?? false) ? s.EPData?.EnergyPlusConstructionName : "";
            fields.Add(("Energy Plus Construction Name", getEnergyPlusName));

            // Add Comments at the end
            Func<SurfaceTemplateDto, string> getComments = s => s.Comments;
            fields.Add(("Comments", getComments));

            return fields;
        }

        private void CreateSurfaceWorksheet(OfficeOpenXml.ExcelPackage package, string name, List<SurfaceTemplateDto> surfaces)
        {
            if (surfaces == null || !surfaces.Any())
                return;

            var worksheet = package.Workbook.Worksheets.Add(name);

            // Get the category code from the first surface
            string categoryCode = surfaces.First().Category?.ConstructionCategoryCode;

            // Get the field definitions for this category
            var fieldDefinitions = GetSurfaceFieldDefinitions(categoryCode);

            // Add headers
            for (int i = 0; i < fieldDefinitions.Count; i++)
            {
                worksheet.Cells[1, i + 1].Value = fieldDefinitions[i].Header;
            }

            // Add data
            for (int i = 0; i < surfaces.Count; i++)
            {
                var surface = surfaces[i];
                int row = i + 2;

                for (int col = 0; col < fieldDefinitions.Count; col++)
                {
                    worksheet.Cells[row, col + 1].Value = fieldDefinitions[col].ValueGetter(surface);
                }
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            // Center align all cells
            using (var range = worksheet.Cells[1, 1, worksheet.Dimension.End.Row, worksheet.Dimension.End.Column])
            {
                range.Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                range.Style.VerticalAlignment = OfficeOpenXml.Style.ExcelVerticalAlignment.Center;
            }

            // Make headers bold
            using (var range = worksheet.Cells[1, 1, 1, worksheet.Dimension.End.Column])
            {
                range.Style.Font.Bold = true;
            }
        }

        /// <summary>
        /// Gets the field definitions for an opening category
        /// </summary>
        private List<(string Header, Func<OpeningTemplateDto, object> ValueGetter)> GetOpeningFieldDefinitions(string categoryCode)
        {
            // Common fields for all opening categories - in order they appear in the UI
            var fields = new List<(string Header, Func<OpeningTemplateDto, object> ValueGetter)>();

            // General section
            Func<OpeningTemplateDto, string> getCategory = o => o.Category?.Title;
            fields.Add(("Category", getCategory));

            Func<OpeningTemplateDto, string> getDescription = o => o.Description;
            fields.Add(("Description", getDescription));

            Func<OpeningTemplateDto, string> getDisplayDescription = o => o.Description;
            fields.Add(("Display Description", getDisplayDescription));

            Func<OpeningTemplateDto, string> getShowInReport = o => o.ShowInReport ? "Yes" : "No";
            fields.Add(("Show in Report", getShowInReport));

            Func<OpeningTemplateDto, string> getManufacturer = o => o.Manufacturer?.Description;
            fields.Add(("Manufacturer", getManufacturer));

            Func<OpeningTemplateDto, string> getConstructionId = o => o.ExternalConstructionId;
            fields.Add(("Construction ID", getConstructionId));

            // Add Adjacency field for all categories
            Func<OpeningTemplateDto, string> getAdjacency = o => o.Adjacency?.Title;
            fields.Add(("Adjacency", getAdjacency));

            Func<OpeningTemplateDto, string> getUnitOfMeasurement = o => o.UnitOfMeasure?.Title;
            fields.Add(("Unit of Measurement", getUnitOfMeasurement));

            // Add Favourite field
            Func<OpeningTemplateDto, string> getFavourite = o => o.IsFavourite ? "Yes" : "No";
            fields.Add(("Favourite", getFavourite));

            // Add frame material for all opening categories
            Func<OpeningTemplateDto, string> getFrameMaterial = o => o.FrameMaterial?.Title;
            fields.Add(("Frame Material", getFrameMaterial));

            // Add frame colour and solar absorptance for glazing categories
            if (categoryCode == "ExteriorGlazing" || categoryCode == "Skylight" || categoryCode == "RoofWindow")
            {
                Func<OpeningTemplateDto, string> getFrameColour = o => o.FrameColour?.Title;
                fields.Add(("Frame Colour", getFrameColour));

                Func<OpeningTemplateDto, object> getFrameSolarAbsorptance = o => o.FrameSolarAbsorptance;
                fields.Add(("Frame Solar Absorptance", getFrameSolarAbsorptance));
            }

            // Add opening style fields for all categories
            Func<OpeningTemplateDto, string> getOpeningStyle = o => o.OpeningStyle?.Title;
            fields.Add(("Opening Style", getOpeningStyle));

            Func<OpeningTemplateDto, string> getNccOpeningStyle = o => o.NccOpeningStyle?.Title;
            fields.Add(("NCC Opening Style", getNccOpeningStyle));

            // Add weather stripping for all categories except permanent openings
            if (categoryCode != "HorizontalOpening" && categoryCode != "VerticalOpening")
            {
                Func<OpeningTemplateDto, string> getWeatherStripped = o => (o.HasWeatherStrip ?? false) ? "Yes" : "No";
                fields.Add(("Weather Stripped", getWeatherStripped));
            }

            // Add insect screen for all categories except permanent openings
            if (categoryCode != "HorizontalOpening" && categoryCode != "VerticalOpening")
            {
                Func<OpeningTemplateDto, string> getInsectScreen = o => (o.HasInsectScreen ?? false) ? "Yes" : "No";
                fields.Add(("Insect Screen", getInsectScreen));
            }

            // Add glass data for glazing and roof lighting categories
            if (categoryCode == "ExteriorGlazing" || categoryCode == "Skylight" || categoryCode == "RoofWindow")
            {
                Func<OpeningTemplateDto, string> getGlassId = o => o.GlassData?.GlassId;
                fields.Add(("Glass ID", getGlassId));

                Func<OpeningTemplateDto, string> getGlassDescription = o => o.GlassData?.Description;
                fields.Add(("Glass Description", getGlassDescription));

                Func<OpeningTemplateDto, string> getGlassType = o => o.GlassData?.Type?.Title;
                fields.Add(("Glass Type", getGlassType));

                Func<OpeningTemplateDto, string> getGlassColour = o => o.GlassData?.Colour?.Title;
                fields.Add(("Glass Colour", getGlassColour));

                Func<OpeningTemplateDto, string> getLaminated = o => (o.GlassData?.IsLaminated ?? false) ? "Yes" : "No";
                fields.Add(("Laminated", getLaminated));

                Func<OpeningTemplateDto, string> getLowECoating = o => (o.GlassData?.HasLowECoating ?? false) ? "Yes" : "No";
                fields.Add(("Low-E Coating", getLowECoating));

                Func<OpeningTemplateDto, object> getGlassThickness = o => o.GlassData?.Thickness;
                fields.Add(("Glass Thickness (mm)", getGlassThickness));

                // Add Tilt for glazing categories
                Func<OpeningTemplateDto, object> getTilt = o => o.Tilt;
                fields.Add(("Tilt (°)", getTilt));
            }

            // Add thermal break for glazing categories
            if (categoryCode == "ExteriorGlazing" || categoryCode == "Skylight" || categoryCode == "RoofWindow")
            {
                Func<OpeningTemplateDto, string> getThermalBreak = o => (o.IsThermalBreak ?? false) ? "Yes" : "No";
                fields.Add(("Thermal Break", getThermalBreak));
            }

            // Add skylight-specific fields
            if (categoryCode == "Skylight" || categoryCode == "RoofWindow")
            {
                Func<OpeningTemplateDto, string> getInteriorShades = o => (o.HasInteriorShades ?? false) ? "Yes" : "No";
                fields.Add(("Interior Shades", getInteriorShades));

                Func<OpeningTemplateDto, string> getExteriorShades = o => (o.HasExteriorShades ?? false) ? "Yes" : "No";
                fields.Add(("Exterior Shades", getExteriorShades));

                // Only add shaft fields if not hidden by default
                var hiddenColumns = Construction.AssignDefaultColumnVisibility(categoryCode);
                if (!hiddenColumns.ContainsKey("shaftLength") || !hiddenColumns["shaftLength"])
                {
                    Func<OpeningTemplateDto, object> getShaftLength = o => o.ShaftLength;
                    fields.Add(("Shaft Length (m)", getShaftLength));

                    Func<OpeningTemplateDto, object> getShaftArea = o => o.ShaftArea;
                    fields.Add(("Shaft Area (m²)", getShaftArea));

                    Func<OpeningTemplateDto, object> getShaftReflectance = o => o.ShaftReflectance;
                    fields.Add(("Shaft Reflectance", getShaftReflectance));

                    Func<OpeningTemplateDto, object> getShaftWallRValue = o => o.ShaftWallRValue;
                    fields.Add(("Shaft Wall R-Value", getShaftWallRValue));

                    Func<OpeningTemplateDto, string> getHasDiffuser = o => (o.HasDiffuser ?? false) ? "Yes" : "No";
                    fields.Add(("Has Diffuser", getHasDiffuser));
                }
            }

            // Add Performance section for glazing categories
            if (categoryCode == "ExteriorGlazing" || categoryCode == "Skylight" || categoryCode == "RoofWindow")
            {
                Func<OpeningTemplateDto, object> getUValue = o => o.Performance?.UValue;
                fields.Add(("U-Value", getUValue));

                Func<OpeningTemplateDto, object> getShgc = o => o.Performance?.SHGC;
                fields.Add(("SHGC", getShgc));

                Func<OpeningTemplateDto, object> getVisibleTransmittance = o => o.Performance?.VisibleTransmittance;
                fields.Add(("Visible Transmittance", getVisibleTransmittance));

                Func<OpeningTemplateDto, object> getAirInfiltration = o => o.Performance?.AirInfiltration;
                fields.Add(("Air Infiltration", getAirInfiltration));
            }

            // Add Life Cycle and Cost section
            Func<OpeningTemplateDto, string> getLifeCycleAndCost = o => (o.LifeCycleData?.HasConstructionLifeCycleAndCost ?? false) ? "Yes" : "No";
            fields.Add(("Life Cycle and Cost", getLifeCycleAndCost));

            // Add lifecycle child fields for all categories
            Func<OpeningTemplateDto, object> getCost = o =>
                (o.LifeCycleData?.HasConstructionLifeCycleAndCost ?? false) ? o.LifeCycleData?.CostPerM2 : null;
            fields.Add(("Cost ($)", getCost));

            Func<OpeningTemplateDto, object> getEmbodiedEnergy = o =>
                (o.LifeCycleData?.HasConstructionLifeCycleAndCost ?? false) ? o.LifeCycleData?.EmbodiedEnergy : null;
            fields.Add(("Embodied Energy (MJ)", getEmbodiedEnergy));

            Func<OpeningTemplateDto, object> getEmbodiedWater = o =>
                (o.LifeCycleData?.HasConstructionLifeCycleAndCost ?? false) ? o.LifeCycleData?.EmbodiedWater : null;
            fields.Add(("Embodied Water (L)", getEmbodiedWater));

            Func<OpeningTemplateDto, object> getEmbodiedGHG = o =>
                (o.LifeCycleData?.HasConstructionLifeCycleAndCost ?? false) ? o.LifeCycleData?.EmbodiedGhgEmissions : null;
            fields.Add(("Embodied GHG Emissions (kgCO₂e)", getEmbodiedGHG));

            // Add Chenath Scratch File section
            Func<OpeningTemplateDto, string> getChenathScratchFile = o => (o.ChenathData?.HasData ?? false) ? "Yes" : "No";
            fields.Add(("Chenath Scratch File", getChenathScratchFile));

            // Add Chenath child fields
            Func<OpeningTemplateDto, string> getConstructionDescription = o =>
                (o.ChenathData?.HasData ?? false) ? o.ChenathData?.ConstructionDescription : "";
            fields.Add(("Construction Description", getConstructionDescription));

            Func<OpeningTemplateDto, string> getConstructionCodeRange = o =>
                (o.ChenathData?.HasData ?? false) ? (o.ChenathData?.ConstructionTypeCodeRange != null ?
                    $"{o.ChenathData?.ConstructionTypeCodeRange?.Min} - {o.ChenathData?.ConstructionTypeCodeRange?.Max}" : "") : "";
            fields.Add(("Chenath Construction Code Range", getConstructionCodeRange));

            // Add construction code per software
            var assessmentSoftwareList = _unitOfWork.Context.RSS_AssessmentSoftware
                .Where(a => !a.Deleted)
                .OrderBy(a => a.Description)
                .ToList();

            foreach (var software in assessmentSoftwareList)
            {
                string softwareCode = software.AssessmentSoftwareCode.ToLower();
                string softwareName = software.Description;

                Func<OpeningTemplateDto, string> getConstructionCodeForSoftware = o =>
                    (o.ChenathData?.HasData ?? false) &&
                    (o.ChenathData?.ConstructionCodePerSoftware?.ContainsKey(softwareCode) ?? false) ?
                    o.ChenathData?.ConstructionCodePerSoftware[softwareCode] : "";

                fields.Add(($"{softwareName} Construction Code", getConstructionCodeForSoftware));
            }

            // Add EnergyPlus IDF File section
            Func<OpeningTemplateDto, string> getEnergyPlusIdfFile = o => (o.EPData?.HasData ?? false) ? "Yes" : "No";
            fields.Add(("EnergyPlus IDF File", getEnergyPlusIdfFile));

            // Add EnergyPlus child fields
            Func<OpeningTemplateDto, string> getEnergyPlusName = o =>
                (o.EPData?.HasData ?? false) ? o.EPData?.EnergyPlusConstructionName : "";
            fields.Add(("Energy Plus Construction Name", getEnergyPlusName));

            // Add Comments at the end
            Func<OpeningTemplateDto, string> getComments = o => o.Comments;
            fields.Add(("Comments", getComments));

            return fields;
        }

        private void CreateOpeningWorksheet(OfficeOpenXml.ExcelPackage package, string name, List<OpeningTemplateDto> openings)
        {
            if (openings == null || !openings.Any())
                return;

            var worksheet = package.Workbook.Worksheets.Add(name);

            // Get the category code from the first opening
            string categoryCode = openings.First().Category?.ConstructionCategoryCode;

            // Get the field definitions for this category
            var fieldDefinitions = GetOpeningFieldDefinitions(categoryCode);

            // Add headers
            for (int i = 0; i < fieldDefinitions.Count; i++)
            {
                worksheet.Cells[1, i + 1].Value = fieldDefinitions[i].Header;
            }

            // Add data
            for (int i = 0; i < openings.Count; i++)
            {
                var opening = openings[i];
                int row = i + 2;

                for (int col = 0; col < fieldDefinitions.Count; col++)
                {
                    worksheet.Cells[row, col + 1].Value = fieldDefinitions[col].ValueGetter(opening);
                }
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            // Center align all cells
            using (var range = worksheet.Cells[1, 1, worksheet.Dimension.End.Row, worksheet.Dimension.End.Column])
            {
                range.Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                range.Style.VerticalAlignment = OfficeOpenXml.Style.ExcelVerticalAlignment.Center;
            }

            // Make headers bold
            using (var range = worksheet.Cells[1, 1, 1, worksheet.Dimension.End.Column])
            {
                range.Style.Font.Bold = true;
            }
        }

        /// <summary>
        /// Accepts an excel file and attempts to process it. During the first process, any warnings or
        /// errors should require the user to click "ok, I get it, process anyway" - at this point
        /// query again with forceImport = true.
        /// </summary>
        /// <returns></returns>
        [Authorize(Roles = "settings__uploaddatasets__view")]
        [HttpPost]
        public async Task<IHttpActionResult> UploadConstructionDatabase(string type, bool forceImport = false)
        {
            type = type.ToLower();
            if (type != "construction" && type != "opening")
                return BadRequest("Incorrect 'type' specified. Must be either 'construction' or 'opening'");

            var stream = await Request.Content.ReadAsStreamAsync();
            if (stream.Length == 0)
                return BadRequest("No content found. Did you add an excel file to the content-body and set the mime-type?");

            // Used to keep track of any warnings or errors generated during the import process.
            // Will be returned to the user so they can diagnose any problems...
            var report = new ProcessReport();

            try
            {
                // Validate Excel sheet names before processing and get list of missing sheets
                var missingSheets = await ValidateExcelSheetNames(stream, type, report);

                // Perform actual transformation of data from excel file to DTO's.
                // We just create both mappers regardless as the openings dataset requires both.
                ExcelMapper surfaceMapper = await CreateMapperForSurfaceData(stream, report);
                ExcelMapper openingMapper = await CreateMapperForOpeningData(stream, report);

                IEnumerable<SurfaceTemplateDto> surfaces = null;
                IEnumerable<OpeningTemplateDto> openings = null;

                if (type == "construction")
                {
                    // The 'construction database' excel file is ALL surfaces.
                    var roofs = FetchWithSheetNameValidation<SurfaceTemplateDto>(surfaceMapper, "Roof", report, missingSheets);
                    var ceilings = FetchWithSheetNameValidation<SurfaceTemplateDto>(surfaceMapper, "Ceiling (Roof-Roof Space Above)", report, missingSheets);
                    var ceilingNeighbours = FetchWithSheetNameValidation<SurfaceTemplateDto>(surfaceMapper, "Ceiling (Neighbour Above)", report, missingSheets);
                    var exteriorWalls = FetchWithSheetNameValidation<SurfaceTemplateDto>(surfaceMapper, "Exterior Walls", report, missingSheets);
                    var subfloorWalls = FetchWithSheetNameValidation<SurfaceTemplateDto>(surfaceMapper, "Subfloor Walls", report, missingSheets);

                    var interiorWalls = FetchWithSheetNameValidation<SurfaceTemplateDto>(surfaceMapper, "Interior Walls (Partition)", report, missingSheets);
                    var interiorWallsRoof = FetchWithSheetNameValidation<SurfaceTemplateDto>(surfaceMapper, "Interior Walls (Roof Space)", report, missingSheets);
                    var interiorWallsSubfloor = FetchWithSheetNameValidation<SurfaceTemplateDto>(surfaceMapper, "Interior Walls (Subfloor Space)", report, missingSheets);

                    var interiorWallsNeighbour = FetchWithSheetNameValidation<SurfaceTemplateDto>(surfaceMapper, "Interior Walls (Neighbour)", report, missingSheets);
                    var floorGround = FetchWithSheetNameValidation<SurfaceTemplateDto>(surfaceMapper, "Exterior Floor (Ground)", report, missingSheets);
                    var floorSuspended = FetchWithSheetNameValidation<SurfaceTemplateDto>(surfaceMapper, "Exterior Floor (Suspended)", report, missingSheets);
                    var floorElevated = FetchWithSheetNameValidation<SurfaceTemplateDto>(surfaceMapper, "Exterior Floor (Elevated)", report, missingSheets);
                    var floorIntermediate = FetchWithSheetNameValidation<SurfaceTemplateDto>(surfaceMapper, "Intermediate Floor", report, missingSheets);
                    var floorIntermediateNeighbour = FetchWithSheetNameValidation<SurfaceTemplateDto>(surfaceMapper, "Intermediate Floor (Neighbour)", report, missingSheets);

                    surfaces = roofs.Concat(ceilings).Concat(exteriorWalls).Concat(subfloorWalls).Concat(interiorWalls)
                        .Concat(interiorWallsRoof).Concat(interiorWallsSubfloor)
                        .Concat(floorGround).Concat(floorSuspended).Concat(floorElevated).Concat(floorIntermediate)
                        .Concat(ceilingNeighbours).Concat(interiorWallsNeighbour).Concat(floorIntermediateNeighbour);

                    // Also check for any Opening sheets that might be present in Construction files
                    // This ensures AddAfterMapping<OpeningTemplateDto> callbacks get triggered
                    var exteriorDoors = FetchWithSheetNameValidation<SurfaceTemplateDto>(surfaceMapper, "Exterior Door", report, missingSheets);
                    var interiorDoors = FetchWithSheetNameValidation<SurfaceTemplateDto>(surfaceMapper, "Interior Door", report, missingSheets);
                    if (exteriorDoors.Any() || interiorDoors.Any())
                    {
                        surfaces = surfaces.Concat(exteriorDoors).Concat(interiorDoors);
                    }

                    var exteriorGlazing = FetchWithSheetNameValidation<OpeningTemplateDto>(openingMapper, "Exterior Glazing", report, missingSheets);
                    var skylights = FetchWithSheetNameValidation<OpeningTemplateDto>(openingMapper, "Skylight", report, missingSheets);
                    var roofWindows = FetchWithSheetNameValidation<OpeningTemplateDto>(openingMapper, "Roof Window", report, missingSheets);
                    if (exteriorGlazing.Any() || skylights.Any() || roofWindows.Any())
                    {
                        openings = exteriorGlazing.Concat(skylights).Concat(roofWindows);
                    }
                }
                else if (type == "opening")
                {
                    // The 'openings database' excel file is a mixture of surfaces and openings (lol)
                    var exteriorDoors = FetchWithSheetNameValidation<SurfaceTemplateDto>(surfaceMapper, "Exterior Door", report, missingSheets);
                    var interiorDoors = FetchWithSheetNameValidation<SurfaceTemplateDto>(surfaceMapper, "Interior Door", report, missingSheets);
                    surfaces = exteriorDoors.Concat(interiorDoors);

                    var exteriorGlazing = FetchWithSheetNameValidation<OpeningTemplateDto>(openingMapper, "Exterior Glazing", report, missingSheets);
                    // Interior glazing tab is left out for now as it is always blank (Alistair will work on this in future)
                    //var interiorGlazing = FetchWithSheetNameValidation<OpeningTemplateDto>(openingMapper, "Interior Glazing", report, missingSheets);
                    var skylights = FetchWithSheetNameValidation<OpeningTemplateDto>(openingMapper, "Skylight", report, missingSheets);
                    var roofWindows = FetchWithSheetNameValidation<OpeningTemplateDto>(openingMapper, "Roof Window", report, missingSheets);
                    openings = exteriorGlazing.Concat(skylights).Concat(roofWindows);
                }

                // For the 'pre-process' we will only import the actual data to our database
                // if there are NO errors or warnings at all. Otherwise the user must confirm
                // they are happy with the warnings or errors shown.
                if ((report.Warnings.Count == 0 && report.Errors.Count == 0) || forceImport)
                {
                    if (surfaces != null && surfaces.Any())
                        report += BulkUpsert<SurfaceTemplateDto, RSS_SurfaceTemplate>(surfaces, _unitOfWork, _mapper, _unitOfWork.Context.RSS_SurfaceTemplate);
                    if (openings != null && openings.Any())
                        report += BulkUpsert<OpeningTemplateDto, RSS_OpeningTemplate>(openings, _unitOfWork, _mapper, _unitOfWork.Context.RSS_OpeningTemplate);

                    report.Success = true;
                    return Ok(report);
                }
                else
                {
                    report.Success = false;
                    return Ok(report);
                }
            }
            catch (Exception e)
            {
                report.Errors.Insert(0, $"UNCAUGHT EXCEPTION: {e.Message}");
                report.Success = false;
                return Ok(report);
            }
        }

        private async Task<ExcelMapper> CreateMapperForSurfaceData(Stream stream, ProcessReport warnings)
        {
            // Actually reading the stream with the excel mapper causes it to be disposed,
            // so we want to create a copy.
            MemoryStream backup = new MemoryStream();
            stream.Position = 0;
            await stream.CopyToAsync(backup);
            backup.Position = 0;

            // Load all required 'enum' types.
            var categories = _unitOfWork.Context.RSS_ConstructionCategory.ProjectTo<ConstructionCategoryDto>(_mapper.ConfigurationProvider);
            var subCategories = _unitOfWork.Context.RSS_ConstructionSubCategory.ProjectTo<ConstructionSubCategoryDto>(_mapper.ConfigurationProvider);
            var adjacencies = _unitOfWork.Context.RSS_Adjacency.ProjectTo<AdjacencyDto>(_mapper.ConfigurationProvider);
            var units = _unitOfWork.Context.RSS_UnitOfMeasure.ProjectTo<UnitOfMeasureDto>(_mapper.ConfigurationProvider);
            var airCavities = _unitOfWork.Context.RSS_AirCavity.ProjectTo<AirCavityDto>(_mapper.ConfigurationProvider);
            var manufacturers = _unitOfWork.Context.RSS_Manufacturer.ProjectTo<ManufacturerDto>(_mapper.ConfigurationProvider);
            var colours = _unitOfWork.Context.RSS_Colour.ProjectTo<ColourDto>(_mapper.ConfigurationProvider);
            var openingStyles = _unitOfWork.Context.RSS_OpeningStyle.ProjectTo<OpeningStyleDto>(_mapper.ConfigurationProvider);
            var nccOpeningStyles = _unitOfWork.Context.RSS_NccOpeningStyle.ProjectTo<NccOpeningStyleDto>(_mapper.ConfigurationProvider);
            var assessmentSoftware = _unitOfWork.Context.RSS_AssessmentSoftware.ProjectTo<AssessmentSoftwareDto>(_mapper.ConfigurationProvider);

            ExcelMapper mapper = new ExcelMapper(backup);

            var now = DateTime.Now;
            var user = UtilityFunctions.CurrentUserName ?? "unknown";

            // Custom Mappings for general 'Surface' constructs.
            mapper.AddBeforeMapping<SurfaceTemplateDto>((p, i) =>
            {
                // Before our mapping begins we make sure sub-types are initialized if needed.
                p.InsulationData = new InsulationData();
                p.ChenathData = new ChenathData();
                p.ChenathData.ConstructionCodePerSoftware = new Dictionary<string, string>();
                p.ConstructionId = Guid.NewGuid();
                p.CreatedOn = now;
                p.CreatedByName = user;
                p.Deleted = false;
                p.EPData = new EPData();
                p.ShowInReport = true;
            });

            mapper.AddMapping<SurfaceTemplateDto>("Category", p => p.Category)
                .SetPropertyUsing(v => categories.FirstOrDefault(x => x.Title == (string)v));

            mapper.AddMapping<SurfaceTemplateDto>("Construction Type", p => p.SubCategory)
                .SetPropertyUsing(v => subCategories.FirstOrDefault(x => x.Title == (string)v));

            mapper.AddMapping<SurfaceTemplateDto>("Manufacturer", p => p.Manufacturer)
                .SetPropertyUsing((v, cell) =>
                {
                    var data = manufacturers.FirstOrDefault(x => x.Description == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Manufacturer - Manufacturer {v} does not exist. Cell: {cell.Address}";
                        warnings.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });

            // Map Construction ID field
            mapper.AddMapping<SurfaceTemplateDto>("Construction ID", p => p.ExternalConstructionId);
            mapper.AddMapping<SurfaceTemplateDto>("Construction Id", p => p.ExternalConstructionId); // Alternative spelling

            // Map basic fields
            mapper.AddMapping<SurfaceTemplateDto>("Description", p => p.Description);
            mapper.AddMapping<SurfaceTemplateDto>("Display Description", p => p.DisplayDescription);

            // Map Floor Covering field for floor categories
            mapper.AddMapping<SurfaceTemplateDto>("Floor Covering", p => p.FloorCovering);

            // Map physical properties
            mapper.AddMapping<SurfaceTemplateDto>("Thickness (mm)", p => p.Thickness);
            mapper.AddMapping<SurfaceTemplateDto>("Density (kg/m²)", p => p.Density);
            mapper.AddMapping<SurfaceTemplateDto>("Tilt (°)", p => p.Tilt);
            mapper.AddMapping<SurfaceTemplateDto>("System R-Value", p => p.SystemRValue);

            mapper.AddMapping<SurfaceTemplateDto>("Adjacency", p => p.Adjacency)
                .SetPropertyUsing((v, cell) =>
                {

                    var data = adjacencies.FirstOrDefault(x => x.Title == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Adjacency - Adjacency {v} does not exist. Cell: {cell.Address}";
                        warnings.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });

            mapper.AddMapping<SurfaceTemplateDto>("Unit of Measurement", p => p.UnitOfMeasure)
                .SetPropertyUsing(v => units.FirstOrDefault(x => x.Title == (string)v));

            mapper.AddMapping<SurfaceTemplateDto>("Exterior Colour", p => p.ExteriorColour)
                .SetPropertyUsing((v, cell) =>
                {
                    var data = colours.FirstOrDefault(x => x.Title == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Colour - Colour {v} does not exist. Cell: {cell.Address}";
                        warnings.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });

            mapper.AddMapping<SurfaceTemplateDto>("Interior Colour", p => p.InteriorColour)
                .SetPropertyUsing((v, cell) =>
                {
                    var data = colours.FirstOrDefault(x => x.Title == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Colour - Colour {v} does not exist. Cell: {cell.Address}";
                        warnings.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });

            mapper.AddMapping<SurfaceTemplateDto>("Thermal Bridge", p => p.ThermalBridge)
                .SetPropertyUsing(v => (string)v == "Yes");

            mapper.AddMapping<SurfaceTemplateDto>("Full Masonry", p => p.IsFullMasonry)
                .SetPropertyUsing(v => (string)v == "Yes");

            mapper.AddMapping<SurfaceTemplateDto>("Show in Report", p => p.ShowInReport)
                .SetPropertyUsing(v => (string)v == "Yes");

            // Map Favourite field
            mapper.AddMapping<SurfaceTemplateDto>("Favourite", p => p.IsFavourite)
                .SetPropertyUsing(v => (string)v == "Yes");

            // Map lifecycle data fields
            mapper.AddMapping<ConstructionLifeCycleAndCost>("Cost ($)", p => p.CostPerM2)
                .SetPropertyUsing((v, cell) =>
                {
                    if (v == null || string.IsNullOrWhiteSpace(v.ToString()))
                        return null;

                    decimal result;
                    if (decimal.TryParse(v.ToString(), out result))
                        return result;

                    string warning = $"WARNING: Invalid Cost value '{v}' - must be a decimal number. Cell: {cell.Address}";
                    warnings.Warnings.Add(warning);
                    return null;
                });

            mapper.AddMapping<ConstructionLifeCycleAndCost>("Embodied Energy (MJ)", p => p.EmbodiedEnergy)
                .SetPropertyUsing((v, cell) =>
                {
                    if (v == null || string.IsNullOrWhiteSpace(v.ToString()))
                        return null;

                    decimal result;
                    if (decimal.TryParse(v.ToString(), out result))
                        return result;

                    string warning = $"WARNING: Invalid Embodied Energy value '{v}' - must be a decimal number. Cell: {cell.Address}";
                    warnings.Warnings.Add(warning);
                    return null;
                });

            mapper.AddMapping<ConstructionLifeCycleAndCost>("Embodied Water (L)", p => p.EmbodiedWater)
                .SetPropertyUsing((v, cell) =>
                {
                    if (v == null || string.IsNullOrWhiteSpace(v.ToString()))
                        return null;

                    decimal result;
                    if (decimal.TryParse(v.ToString(), out result))
                        return result;

                    string warning = $"WARNING: Invalid Embodied Water value '{v}' - must be a decimal number. Cell: {cell.Address}";
                    warnings.Warnings.Add(warning);
                    return null;
                });

            mapper.AddMapping<ConstructionLifeCycleAndCost>("Embodied GHG Emissions (kgCO₂e)", p => p.EmbodiedGhgEmissions)
                .SetPropertyUsing((v, cell) =>
                {
                    if (v == null || string.IsNullOrWhiteSpace(v.ToString()))
                        return null;

                    decimal result;
                    if (decimal.TryParse(v.ToString(), out result))
                        return result;

                    string warning = $"WARNING: Invalid Embodied GHG Emissions value '{v}' - must be a decimal number. Cell: {cell.Address}";
                    warnings.Warnings.Add(warning);
                    return null;
                });

            // Map air cavities. In this case there can be multiple headings for the same column
            // so we need to add mappings for all possible headings...
            mapper.AddMapping<SurfaceTemplateDto>("Roof Space Ventilation", p => p.AirCavity)
                .SetPropertyUsing(v => airCavities.FirstOrDefault(x => x.Title == (string)v));
            mapper.AddMapping<SurfaceTemplateDto>("Air Cavity", p => p.AirCavity)
               .SetPropertyUsing(v => airCavities.FirstOrDefault(x => x.Title == (string)v));
            mapper.AddMapping<SurfaceTemplateDto>("Subfloor Ventilation", p => p.AirCavity)
               .SetPropertyUsing(v => airCavities.FirstOrDefault(x => x.Title == (string)v));

            // This mapping is for exterior and interior doors only (reside in opening excel database)...
            mapper.AddMapping<SurfaceTemplateDto>("Weather Stripped", p => p.HasWeatherStrip)
              .SetPropertyUsing(v => (string)v == "Yes");
            mapper.AddMapping<SurfaceTemplateDto>("Insect Screen", p => p.HasInsectScreen)
              .SetPropertyUsing(v => (string)v == "Yes");
            mapper.AddMapping<SurfaceTemplateDto>("Opening Style", p => p.OpeningStyle)
                .SetPropertyUsing((v, cell) =>
                {
                    var data = openingStyles.FirstOrDefault(x => x.Title == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Opening Style - Opening Style {v} does not exist. Cell: {cell.Address}";
                        warnings.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });
            mapper.AddMapping<SurfaceTemplateDto>("NCC Opening Style", p => p.NccOpeningStyle)
                .SetPropertyUsing((v, cell) =>
                {
                    var data = nccOpeningStyles.FirstOrDefault(x => x.Title == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown NCC Opening Style - NCC Opening Style {v} does not exist. Cell: {cell.Address}";
                        warnings.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });

            // Map insulation data
            mapper.AddMapping<InsulationData>("Insulation", p => p.HasData)
               .SetPropertyUsing(v => (string)v == "Yes");
            mapper.AddMapping<InsulationData>("Insulation Description", p => p.Description);
            mapper.AddMapping<InsulationData>("Insulation R-Value", p => p.RValue);
            mapper.AddMapping<InsulationData>("Bulk Insulation", p => p.IsBulk)
               .SetPropertyUsing(v => (string)v == "Yes");
            mapper.AddMapping<InsulationData>("Reflective Insulation", p => p.IsReflective)
               .SetPropertyUsing(v => (string)v == "Yes");
            mapper.AddMapping<InsulationData>("Perimeter Insulation", p => p.HasSlabEdgeInsulation)
               .SetPropertyUsing(v => (string)v == "Yes");
            mapper.AddMapping<InsulationData>("Perimeter Insulation R-Value", p => p.SlabEdgeInsulationRValue);

            // Map CHENATH data
            mapper.AddMapping<ChenathData>("Chenath Scratch File", p => p.HasData)
                .SetPropertyUsing(v => (string)v == "Yes");
            mapper.AddMapping<ChenathData>("Construction Description", p => p.ConstructionDescription);
            mapper.AddMapping<ChenathData>("Chenath Construction Code Range", p => p.ConstructionTypeCodeRange)
                .SetPropertyUsing((v, cell) =>
                {
                    if (v == null || string.IsNullOrWhiteSpace(v.ToString()))
                        return null;

                    string cellValue = v.ToString().Trim();
                    Dtos.Range<int> matchedRange = null;

                    // Check if the cell value matches any of the predefined ranges
                    foreach (var kvp in ChenathData.ChenathConstructionRanges)
                    {
                        var range = kvp.Value;
                        string expectedRangeString = $"{range.Min} - {range.Max}";

                        // Check for exact match or common variations
                        if (cellValue == expectedRangeString ||
                            cellValue == $"{range.Min}-{range.Max}" ||
                            cellValue == $"{range.Min} to {range.Max}" ||
                            cellValue == $"{range.Min}–{range.Max}") // em dash
                        {
                            matchedRange = range;
                            break;
                        }
                    }

                    if (matchedRange == null)
                    {
                        string warning = $"WARNING: Failed to find matching CHENATH Construction Code Range for '{cellValue}'. Expected format: 'min - max' (e.g., '241 - 340'). Cell: {cell.Address}";
                        warnings.Warnings.Add(warning);
                    }

                    return matchedRange;
                });

            // Map lifecycle data
            mapper.AddMapping<ConstructionLifeCycleAndCost>("Life Cycle and Cost", p => p.HasConstructionLifeCycleAndCost)
                .SetPropertyUsing(v => (string)v == "Yes");

            // Map EP Data
            mapper.AddMapping<EPData>("EnergyPlus IDF File", p => p.HasData)
                .SetPropertyUsing(v => (string)v == "Yes");
            mapper.AddMapping<EPData>("Energy Plus Construction Name", p => p.EnergyPlusConstructionName);
            mapper.AddMapping<EPData>("EnergyPlus Construction Name", p => p.EnergyPlusConstructionName);

            // Map Comments field
            mapper.AddMapping<SurfaceTemplateDto>("Comments", p => p.Comments);

            // Map construction codes using temporary fields - these will be consolidated in post-mapping
            // Dynamic mappings based on database assessment software
            foreach (var software in assessmentSoftware)
            {
                var softwareCode = software.AssessmentSoftwareCode.ToLower();
                var fieldName = $"{software.Description} Construction Code";

                // Map to appropriate temp field based on software code
                if (softwareCode == "asaccurate")
                    mapper.AddMapping<SurfaceTemplateDto>(fieldName, p => p.TempAccuRateConstructionCode);
                else if (softwareCode == "asberspro")
                    mapper.AddMapping<SurfaceTemplateDto>(fieldName, p => p.TempBERSProConstructionCode);
                else if (softwareCode == "asfirstrate5")
                    mapper.AddMapping<SurfaceTemplateDto>(fieldName, p => p.TempFirstRate5ConstructionCode);
                else if (softwareCode == "asenergyplus")
                    mapper.AddMapping<SurfaceTemplateDto>(fieldName, p => p.TempEnergyPlusConstructionCode);
                else if (softwareCode == "ashero")
                    mapper.AddMapping<SurfaceTemplateDto>(fieldName, p => p.TempHEROConstructionCode);
            }

            // After everything is mapped we set some values that are inferred via other now-mapped values.
            mapper.AddAfterMapping<SurfaceTemplateDto>((p, i) =>
            {
                var lifecycleData = p.LifeCycleData;
                bool lifecycleHasData = lifecycleData.CostPerM2 != null || lifecycleData.EmbodiedEnergy != null ||
                           lifecycleData.EmbodiedWater != null || lifecycleData.EmbodiedGhgEmissions != null;

                p.LifeCycleData.HasConstructionLifeCycleAndCost = lifecycleHasData;
                p.ChenathData.HasData = p.ChenathData.ConstructionTypeCodeRange != null;
                p.EPData.HasData = p.EPData.EnergyPlusConstructionName != null && p.EPData.EnergyPlusConstructionName != "";

                // Transfer temporary construction code values to ChenathData dictionary
                if (p.ChenathData.ConstructionCodePerSoftware == null)
                    p.ChenathData.ConstructionCodePerSoftware = new Dictionary<string, string>();

                if (!string.IsNullOrEmpty(p.TempAccuRateConstructionCode))
                    p.ChenathData.ConstructionCodePerSoftware["asaccurate"] = p.TempAccuRateConstructionCode;

                if (!string.IsNullOrEmpty(p.TempBERSProConstructionCode))
                    p.ChenathData.ConstructionCodePerSoftware["asberspro"] = p.TempBERSProConstructionCode;

                if (!string.IsNullOrEmpty(p.TempFirstRate5ConstructionCode))
                    p.ChenathData.ConstructionCodePerSoftware["asfirstrate5"] = p.TempFirstRate5ConstructionCode;

                if (!string.IsNullOrEmpty(p.TempEnergyPlusConstructionCode))
                    p.ChenathData.ConstructionCodePerSoftware["asenergyplus"] = p.TempEnergyPlusConstructionCode;

                if (!string.IsNullOrEmpty(p.TempHEROConstructionCode))
                    p.ChenathData.ConstructionCodePerSoftware["ashero"] = p.TempHEROConstructionCode;

                // Not exactly sure why (something to do with these values being set by the mapper via reflection...?) the
                // underlying JSON parts weren't updating, and thus saving to the DB wrong. This self-reassignment fixes that.
                p.ChenathData = p.ChenathData;
                p.LifeCycleData = p.LifeCycleData;
                p.EPData = p.EPData;

                if (p.Category != null)
                    p.HiddenTableColumns = Construction.AssignDefaultColumnVisibility(p.Category.ConstructionCategoryCode);
            });

            // Add the same OpeningTemplateDto after mapping logic to the surface mapper as well
            // This ensures the callback runs for both Construction and Opening imports
            mapper.AddAfterMapping<OpeningTemplateDto>((p, i) =>
            {
                var lifecycleData = p.LifeCycleData;
                bool lifecycleHasData = lifecycleData.CostPerM2 != null || lifecycleData.EmbodiedEnergy != null ||
                           lifecycleData.EmbodiedWater != null || lifecycleData.EmbodiedGhgEmissions != null;

                p.LifeCycleData.HasConstructionLifeCycleAndCost = lifecycleHasData;
                p.ChenathData.HasData = p.ChenathData.ConstructionTypeCodeRange != null;
                p.EPData.HasData = p.EPData.EnergyPlusConstructionName != null && p.EPData.EnergyPlusConstructionName != "";

                // Transfer temporary Excel import values to Performance object
                if (p.Performance == null)
                    p.Performance = new OpeningPerformance();

                p.Performance.UValue = p.TempUValue;
                p.Performance.SHGC = p.TempSHGC;
                p.Performance.VisibleTransmittance = p.TempVisibleTransmittance;
                p.Performance.AirInfiltration = p.TempAirInfiltration;

                // Transfer temporary construction code values to ChenathData dictionary
                if (p.ChenathData.ConstructionCodePerSoftware == null)
                    p.ChenathData.ConstructionCodePerSoftware = new Dictionary<string, string>();

                if (!string.IsNullOrEmpty(p.TempAccuRateConstructionCode))
                    p.ChenathData.ConstructionCodePerSoftware["asaccurate"] = p.TempAccuRateConstructionCode;

                if (!string.IsNullOrEmpty(p.TempBERSProConstructionCode))
                    p.ChenathData.ConstructionCodePerSoftware["asberspro"] = p.TempBERSProConstructionCode;

                if (!string.IsNullOrEmpty(p.TempFirstRate5ConstructionCode))
                    p.ChenathData.ConstructionCodePerSoftware["asfirstrate5"] = p.TempFirstRate5ConstructionCode;

                if (!string.IsNullOrEmpty(p.TempEnergyPlusConstructionCode))
                    p.ChenathData.ConstructionCodePerSoftware["asenergyplus"] = p.TempEnergyPlusConstructionCode;

                if (!string.IsNullOrEmpty(p.TempHEROConstructionCode))
                    p.ChenathData.ConstructionCodePerSoftware["ashero"] = p.TempHEROConstructionCode;

                // Not exactly sure why (something to do with these values being set by the mapper via reflection...?) the
                // underlying JSON parts weren't updating, and thus saving to the DB wrong. This self-reassignment fixes that.
                p.ChenathData = p.ChenathData;
                p.LifeCycleData = p.LifeCycleData;
                p.EPData = p.EPData;
                p.Performance = p.Performance; // Ensure Performance JSON is updated

                if (p.Category != null)
                    p.HiddenTableColumns = Construction.AssignDefaultColumnVisibility(p.Category.ConstructionCategoryCode);
            });

            // The mapper now has all custom logic associated with it as needed to map construction data.
            return mapper;

        }

        /// <summary>
        /// Validates that all expected Excel sheet names are present, handling both hyphenated and space-separated formats
        /// </summary>
        private async Task<HashSet<string>> ValidateExcelSheetNames(Stream stream, string type, ProcessReport report)
        {
            // Create a copy of the stream for validation
            MemoryStream backup = new MemoryStream();
            stream.Position = 0;
            await stream.CopyToAsync(backup);
            backup.Position = 0;

            var missingSheets = new HashSet<string>();

            using (var package = new OfficeOpenXml.ExcelPackage(backup))
            {
                var actualSheetNames = package.Workbook.Worksheets.Select(ws => ws.Name).ToList();
                var expectedSheetNames = GetExpectedSheetNames(type);

                foreach (var expectedName in expectedSheetNames)
                {
                    if (!IsSheetNamePresent(actualSheetNames, expectedName))
                    {
                        missingSheets.Add(expectedName);
                    }
                }

                // Add warnings for missing sheets
                foreach (var missingSheet in missingSheets)
                {
                    report.Warnings.Add($"WARNING: Expected sheet '{missingSheet}' not found.");
                }

                // Add available sheets information only once at the bottom if there are missing sheets
                if (missingSheets.Any())
                {
                    report.Warnings.Add($"Available sheets: {string.Join(", ", actualSheetNames)}");
                }
            }

            // Reset stream position for subsequent processing
            stream.Position = 0;

            return missingSheets;
        }

        /// <summary>
        /// Gets the expected sheet names for the given import type
        /// </summary>
        private List<string> GetExpectedSheetNames(string type)
        {
            if (type == "construction")
            {
                return new List<string>
                {
                    "Roof",
                    "Ceiling (Roof-Roof Space Above)",
                    "Ceiling (Neighbour Above)",
                    "Exterior Walls",
                    "Subfloor Walls",
                    "Interior Walls (Partition)",
                    "Interior Walls (Roof Space)",
                    "Interior Walls (Subfloor Space)",
                    "Interior Walls (Neighbour)",
                    "Exterior Floor (Ground)",
                    "Exterior Floor (Suspended)",
                    "Exterior Floor (Elevated)",
                    "Intermediate Floor",
                    "Intermediate Floor (Neighbour)"
                };
            }
            else if (type == "opening")
            {
                return new List<string>
                {
                    "Exterior Door",
                    "Interior Door",
                    "Exterior Glazing",
                    "Skylight",
                    "Roof Window"
                };
            }

            return new List<string>();
        }

        /// <summary>
        /// Checks if a sheet name is present, handling both hyphenated and space-separated formats
        /// </summary>
        private bool IsSheetNamePresent(List<string> actualSheetNames, string expectedName)
        {
            // Check exact match first
            if (actualSheetNames.Contains(expectedName))
                return true;

            // Check with hyphens replaced by spaces
            var expectedWithSpaces = expectedName.Replace("-", " ");
            if (actualSheetNames.Contains(expectedWithSpaces))
                return true;

            // Check with spaces replaced by hyphens
            var expectedWithHyphens = expectedName.Replace(" ", "-");
            if (actualSheetNames.Contains(expectedWithHyphens))
                return true;

            return false;
        }

        /// <summary>
        /// Fetches data from a sheet with enhanced name validation, supporting both hyphenated and space-separated formats
        /// </summary>
        private List<T> FetchWithSheetNameValidation<T>(ExcelMapper mapper, string sheetName, ProcessReport report, HashSet<string> missingSheets)
        {
            // If we already know this sheet is missing, don't try to fetch and don't add another warning
            if (missingSheets.Contains(sheetName))
            {
                return new List<T>();
            }

            // Always start by replacing hyphens with spaces as the primary approach
            string normalizedSheetName = sheetName.Replace("-", " ");

            try
            {
                // Try with hyphens replaced by spaces first (primary approach)
                return mapper.Fetch<T>(normalizedSheetName).ToList();
            }
            catch (Exception ex1)
            {
                try
                {
                    // Try original name as fallback
                    return mapper.Fetch<T>(sheetName).ToList();
                }
                catch (Exception ex2)
                {
                    try
                    {
                        // Try with spaces replaced by hyphens as final fallback
                        return mapper.Fetch<T>(sheetName.Replace(" ", "-")).ToList();
                    }
                    catch (Exception ex3)
                    {
                        // Only add this warning if we haven't already warned about the sheet being missing
                        report.Warnings.Add($"WARNING: Could not fetch data from sheet '{sheetName}'. Error: {ex3.Message}");
                        return new List<T>();
                    }
                }
            }
        }

        private async Task<ExcelMapper> CreateMapperForOpeningData(Stream stream, ProcessReport report)
        {
            // Actually reading the stream with the excel mapper causes it to be disposed,
            // so we want to create a copy.
            MemoryStream backup = new MemoryStream();
            stream.Position = 0;
            await stream.CopyToAsync(backup);
            backup.Position = 0;

            // Since Surfaces and Openings actually share a fair bit in common, we use the
            // surface mapper as a base and expand upon it.
            ExcelMapper mapper = await CreateMapperForSurfaceData(backup, report);

            var categories = _unitOfWork.Context.RSS_ConstructionCategory.ProjectTo<ConstructionCategoryDto>(_mapper.ConfigurationProvider);
            var adjacencies = _unitOfWork.Context.RSS_Adjacency.ProjectTo<AdjacencyDto>(_mapper.ConfigurationProvider);
            var units = _unitOfWork.Context.RSS_UnitOfMeasure.ProjectTo<UnitOfMeasureDto>(_mapper.ConfigurationProvider);
            var airCavities = _unitOfWork.Context.RSS_AirCavity.ProjectTo<AirCavityDto>(_mapper.ConfigurationProvider);
            var manufacturers = _unitOfWork.Context.RSS_Manufacturer.ProjectTo<ManufacturerDto>(_mapper.ConfigurationProvider);
            var colours = _unitOfWork.Context.RSS_Colour.ProjectTo<ColourDto>(_mapper.ConfigurationProvider);
            var openingStyles = _unitOfWork.Context.RSS_OpeningStyle.ProjectTo<OpeningStyleDto>(_mapper.ConfigurationProvider);
            var nccOpeningStyles = _unitOfWork.Context.RSS_NccOpeningStyle.ProjectTo<NccOpeningStyleDto>(_mapper.ConfigurationProvider);
            var frames = _unitOfWork.Context.RSS_FrameMaterial.ProjectTo<FrameMaterialDto>(_mapper.ConfigurationProvider);
            var glassTypes = _unitOfWork.Context.RSS_GlassType.ProjectTo<GlassTypeDto>(_mapper.ConfigurationProvider);
            var glassColours = _unitOfWork.Context.RSS_GlassColour.ProjectTo<GlassColourDto>(_mapper.ConfigurationProvider);
            var assessmentSoftware = _unitOfWork.Context.RSS_AssessmentSoftware.ProjectTo<AssessmentSoftwareDto>(_mapper.ConfigurationProvider);

            var now = DateTime.Now;
            var user = UtilityFunctions.CurrentUserName ?? "unknown";

            mapper.AddBeforeMapping<OpeningTemplateDto>((p, i) =>
            {
                // Before our mapping begins we make sure sub-types are initialized if needed.
                p.ChenathData = new ChenathData();
                p.ChenathData.ConstructionCodePerSoftware = new Dictionary<string, string>();
                p.ConstructionId = Guid.NewGuid();
                p.CreatedOn = now;
                p.CreatedByName = user;
                p.Deleted = false;
                p.EPData = new EPData();
                p.GlassData = new GlassData();
                p.Performance = new OpeningPerformance(); // Initialize Performance object
                p.ShowInReport = true;
            });

            // Map OpeningTemplate data.
            mapper.AddMapping<OpeningTemplateDto>("Thermal Break", p => p.IsThermalBreak)
               .SetPropertyUsing(v => (string)v == "Yes");
            mapper.AddMapping<OpeningTemplateDto>("Weather Stripped", p => p.HasWeatherStrip)
              .SetPropertyUsing(v => (string)v == "Yes");
            mapper.AddMapping<OpeningTemplateDto>("Insect Screen", p => p.HasInsectScreen)
              .SetPropertyUsing(v => (string)v == "Yes");
            mapper.AddMapping<SurfaceTemplateDto>("Show in Report", p => p.ShowInReport)
                .SetPropertyUsing(v => (string)v == "Yes");

            mapper.AddMapping<OpeningTemplateDto>("Frame", p => p.FrameMaterial)
                .SetPropertyUsing((v, cell) =>
                {

                    var data = frames.FirstOrDefault(x => x.Title == (string)v);

                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Frame - Frame {v} does not exist. Cell: {cell.Address}";
                        report.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });
            mapper.AddMapping<OpeningTemplateDto>("Frame Colour", p => p.FrameColour)
                .SetPropertyUsing((v, cell) =>
                {
                    var data = colours.FirstOrDefault(x => x.Title == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Frame Colour - Frame Colour {v} does not exist. Cell: {cell.Address}";
                        report.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });
            mapper.AddMapping<OpeningTemplateDto>("Frame Solar Absorptance", p => p.FrameSolarAbsorptance);
            mapper.AddMapping<OpeningTemplateDto>("Tilt (°)", p => p.Tilt);
            mapper.AddMapping<OpeningTemplateDto>("Tilt", p => p.Tilt); // Alternative name without degree symbol
            mapper.AddMapping<OpeningTemplateDto>("Opening Style", p => p.OpeningStyle)
                .SetPropertyUsing((v, cell) =>
                {
                    var data = openingStyles.FirstOrDefault(x => x.Title == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Opening Style - Opening Style {v} does not exist. Cell: {cell.Address}";
                        report.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });
            mapper.AddMapping<OpeningTemplateDto>("NCC Opening Style", p => p.NccOpeningStyle)
                .SetPropertyUsing((v, cell) =>
                {
                    var data = nccOpeningStyles.FirstOrDefault(x => x.Title == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown NCC Opening Style - NCC Opening Style {v} does not exist. Cell: {cell.Address}";
                        report.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });


            ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
            // Unfortunately even though these values are mapped within the surface map function,
            // due to the data types applied in the "Add Mapping" function it won't actually apply
            // automatically to our OpeningTemplateDto, thus we need to re-map... lame.
            mapper.AddMapping<OpeningTemplateDto>("Category", p => p.Category)
                .SetPropertyUsing(v => categories.FirstOrDefault(x => x.Title == (string)v));

            // Map Construction ID field for openings
            mapper.AddMapping<OpeningTemplateDto>("Construction ID", p => p.ExternalConstructionId);
            mapper.AddMapping<OpeningTemplateDto>("Construction Id", p => p.ExternalConstructionId); // Alternative spelling

            // Map basic fields for openings
            mapper.AddMapping<OpeningTemplateDto>("Description", p => p.Description);
            mapper.AddMapping<OpeningTemplateDto>("Display Description", p => p.DisplayDescription);
            mapper.AddMapping<OpeningTemplateDto>("Show in Report", p => p.ShowInReport)
                .SetPropertyUsing(v => (string)v == "Yes");

            mapper.AddMapping<OpeningTemplateDto>("Manufacturer", p => p.Manufacturer)
                .SetPropertyUsing((v, cell) =>
                {
                    var data = manufacturers.FirstOrDefault(x => x.Description == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Manufacturer - Manufacturer {v} does not exist. Cell: {cell.Address}";
                        report.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });

            mapper.AddMapping<OpeningTemplateDto>("Adjacency", p => p.Adjacency)
                .SetPropertyUsing((v, cell) =>
                {

                    var data = adjacencies.FirstOrDefault(x => x.Title == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Adjacency - Adjacency {v} does not exist. Cell: {cell.Address}";
                        report.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });

            mapper.AddMapping<OpeningTemplateDto>("Unit of Measurement", p => p.UnitOfMeasure)
                .SetPropertyUsing(v => units.FirstOrDefault(x => x.Title == (string)v));

            // Map frame material for openings
            mapper.AddMapping<OpeningTemplateDto>("Frame Material", p => p.FrameMaterial)
                .SetPropertyUsing((v, cell) =>
                {
                    var data = frames.FirstOrDefault(x => x.Title == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Frame Material - Frame Material {v} does not exist. Cell: {cell.Address}";
                        report.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });

            // Map performance data for openings using temporary variables
            // These temporary variables work with ExcelMapper and are transferred to Performance object in post-mapping
            mapper.AddMapping<OpeningTemplateDto>("U-Value", p => p.TempUValue);
            mapper.AddMapping<OpeningTemplateDto>("SHGC", p => p.TempSHGC);
            mapper.AddMapping<OpeningTemplateDto>("Visible Transmittance", p => p.TempVisibleTransmittance);
            mapper.AddMapping<OpeningTemplateDto>("Visible Light Transmittance", p => p.TempVisibleTransmittance); // Alternative name for backward compatibility
            mapper.AddMapping<OpeningTemplateDto>("Air Infiltration", p => p.TempAirInfiltration);

            // Map skylight-specific fields
            mapper.AddMapping<OpeningTemplateDto>("Interior Shades", p => p.HasInteriorShades)
                .SetPropertyUsing(v => (string)v == "Yes");
            mapper.AddMapping<OpeningTemplateDto>("Exterior Shades", p => p.HasExteriorShades)
                .SetPropertyUsing(v => (string)v == "Yes");
            mapper.AddMapping<OpeningTemplateDto>("Shaft Length (m)", p => p.ShaftLength);
            mapper.AddMapping<OpeningTemplateDto>("Shaft Area (m²)", p => p.ShaftArea);
            mapper.AddMapping<OpeningTemplateDto>("Shaft Reflectance", p => p.ShaftReflectance);
            mapper.AddMapping<OpeningTemplateDto>("Shaft Wall R-Value", p => p.ShaftWallRValue);
            mapper.AddMapping<OpeningTemplateDto>("Has Diffuser", p => p.HasDiffuser)
                .SetPropertyUsing(v => (string)v == "Yes");

            // Map lifecycle data fields for OpeningTemplateDto
            mapper.AddMapping<ConstructionLifeCycleAndCost>("Cost ($)", p => p.CostPerM2)
                .SetPropertyUsing((v, cell) =>
                {
                    if (v == null || string.IsNullOrWhiteSpace(v.ToString()))
                        return null;

                    decimal result;
                    if (decimal.TryParse(v.ToString(), out result))
                        return result;

                    string warning = $"WARNING: Invalid Cost value '{v}' - must be a decimal number. Cell: {cell.Address}";
                    report.Warnings.Add(warning);
                    return null;
                });

            mapper.AddMapping<ConstructionLifeCycleAndCost>("Embodied Energy (MJ)", p => p.EmbodiedEnergy)
                .SetPropertyUsing((v, cell) =>
                {
                    if (v == null || string.IsNullOrWhiteSpace(v.ToString()))
                        return null;

                    decimal result;
                    if (decimal.TryParse(v.ToString(), out result))
                        return result;

                    string warning = $"WARNING: Invalid Embodied Energy value '{v}' - must be a decimal number. Cell: {cell.Address}";
                    report.Warnings.Add(warning);
                    return null;
                });

            mapper.AddMapping<ConstructionLifeCycleAndCost>("Embodied Water (L)", p => p.EmbodiedWater)
                .SetPropertyUsing((v, cell) =>
                {
                    if (v == null || string.IsNullOrWhiteSpace(v.ToString()))
                        return null;

                    decimal result;
                    if (decimal.TryParse(v.ToString(), out result))
                        return result;

                    string warning = $"WARNING: Invalid Embodied Water value '{v}' - must be a decimal number. Cell: {cell.Address}";
                    report.Warnings.Add(warning);
                    return null;
                });

            mapper.AddMapping<ConstructionLifeCycleAndCost>("Embodied GHG Emissions (kgCO₂e)", p => p.EmbodiedGhgEmissions)
                .SetPropertyUsing((v, cell) =>
                {
                    if (v == null || string.IsNullOrWhiteSpace(v.ToString()))
                        return null;

                    decimal result;
                    if (decimal.TryParse(v.ToString(), out result))
                        return result;

                    string warning = $"WARNING: Invalid Embodied GHG Emissions value '{v}' - must be a decimal number. Cell: {cell.Address}";
                    report.Warnings.Add(warning);
                    return null;
                });

            // This mapping is for exterior and interior doors only (reside in opening excel database)...
            mapper.AddMapping<OpeningTemplateDto>("Weather Stripped", p => p.HasWeatherStrip)
              .SetPropertyUsing(v => (string)v == "Yes");
            mapper.AddMapping<OpeningTemplateDto>("Insect Screen", p => p.HasInsectScreen)
              .SetPropertyUsing(v => (string)v == "Yes");

            /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


            // Map Glass Data
            mapper.AddMapping<GlassData>("Glass ID", p => p.GlassId);
            mapper.AddMapping<GlassData>("Glass Description", p => p.Description);
            mapper.AddMapping<GlassData>("Glass Thickness (mm)", p => p.Thickness);
            mapper.AddMapping<GlassData>("Laminated", p => p.IsLaminated)
               .SetPropertyUsing(v => (string)v == "Yes");
            mapper.AddMapping<GlassData>("Low-E Coating", p => p.HasLowECoating)
               .SetPropertyUsing(v => (string)v == "Yes");
            mapper.AddMapping<GlassData>("Glass Type", p => p.Type)
                .SetPropertyUsing((v, cell) =>
                {
                    var data = glassTypes.FirstOrDefault(x => x.Title == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Glass Type - Glass Type {v} does not exist. Cell: {cell.Address}";
                        report.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });
            mapper.AddMapping<GlassData>("Glass Colour", p => p.Colour)
                .SetPropertyUsing((v, cell) =>
                {
                    var data = glassColours.FirstOrDefault(x => x.Title == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Glass Colour - Glass Colour {v} does not exist. Cell: {cell.Address}";
                        report.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });

            // Map EnergyPlus data for openings
            mapper.AddMapping<EPData>("EnergyPlus IDF File", p => p.HasData)
                .SetPropertyUsing(v => (string)v == "Yes");
            mapper.AddMapping<EPData>("Energy Plus Construction Name", p => p.EnergyPlusConstructionName);
            mapper.AddMapping<EPData>("EnergyPlus Construction Name", p => p.EnergyPlusConstructionName);

            // Map Comments field for openings
            mapper.AddMapping<OpeningTemplateDto>("Comments", p => p.Comments);

            // Map construction codes using temporary fields - these will be consolidated in post-mapping
            // Dynamic mappings based on database assessment software
            foreach (var software in assessmentSoftware)
            {
                var softwareCode = software.AssessmentSoftwareCode.ToLower();
                var fieldName = $"{software.Description} Construction Code";

                // Map to appropriate temp field based on software code
                if (softwareCode == "asaccurate")
                    mapper.AddMapping<OpeningTemplateDto>(fieldName, p => p.TempAccuRateConstructionCode);
                else if (softwareCode == "asberspro")
                    mapper.AddMapping<OpeningTemplateDto>(fieldName, p => p.TempBERSProConstructionCode);
                else if (softwareCode == "asfirstrate5")
                    mapper.AddMapping<OpeningTemplateDto>(fieldName, p => p.TempFirstRate5ConstructionCode);
                else if (softwareCode == "asenergyplus")
                    mapper.AddMapping<OpeningTemplateDto>(fieldName, p => p.TempEnergyPlusConstructionCode);
                else if (softwareCode == "ashero")
                    mapper.AddMapping<OpeningTemplateDto>(fieldName, p => p.TempHEROConstructionCode);
            }

            // Add explicit mappings for common Excel column names
            mapper.AddMapping<OpeningTemplateDto>("AccuRate Construction Code", p => p.TempAccuRateConstructionCode);
            mapper.AddMapping<OpeningTemplateDto>("BERSPro Construction Code", p => p.TempBERSProConstructionCode);
            mapper.AddMapping<OpeningTemplateDto>("FirstRate5 Construction Code", p => p.TempFirstRate5ConstructionCode);
            mapper.AddMapping<OpeningTemplateDto>("EnergyPlus Construction Code", p => p.TempEnergyPlusConstructionCode);
            mapper.AddMapping<OpeningTemplateDto>("HERO Construction Code", p => p.TempHEROConstructionCode);


            mapper.AddAfterMapping<OpeningTemplateDto>((p, i) =>
            {
                var lifecycleData = p.LifeCycleData;
                bool lifecycleHasData = lifecycleData.CostPerM2 != null || lifecycleData.EmbodiedEnergy != null ||
                           lifecycleData.EmbodiedWater != null || lifecycleData.EmbodiedGhgEmissions != null;

                p.LifeCycleData.HasConstructionLifeCycleAndCost = lifecycleHasData;
                p.ChenathData.HasData = p.ChenathData.ConstructionTypeCodeRange != null;
                p.EPData.HasData = p.EPData.EnergyPlusConstructionName != null && p.EPData.EnergyPlusConstructionName != "";

                // Transfer temporary Excel import values to Performance object
                if (p.Performance == null)
                    p.Performance = new OpeningPerformance();

                p.Performance.UValue = p.TempUValue;
                p.Performance.SHGC = p.TempSHGC;
                p.Performance.VisibleTransmittance = p.TempVisibleTransmittance;
                p.Performance.AirInfiltration = p.TempAirInfiltration;

                // Transfer temporary construction code values to ChenathData dictionary
                if (p.ChenathData.ConstructionCodePerSoftware == null)
                    p.ChenathData.ConstructionCodePerSoftware = new Dictionary<string, string>();

                if (!string.IsNullOrEmpty(p.TempAccuRateConstructionCode))
                    p.ChenathData.ConstructionCodePerSoftware["asaccurate"] = p.TempAccuRateConstructionCode;

                if (!string.IsNullOrEmpty(p.TempBERSProConstructionCode))
                    p.ChenathData.ConstructionCodePerSoftware["asberspro"] = p.TempBERSProConstructionCode;

                if (!string.IsNullOrEmpty(p.TempFirstRate5ConstructionCode))
                    p.ChenathData.ConstructionCodePerSoftware["asfirstrate5"] = p.TempFirstRate5ConstructionCode;

                if (!string.IsNullOrEmpty(p.TempEnergyPlusConstructionCode))
                    p.ChenathData.ConstructionCodePerSoftware["asenergyplus"] = p.TempEnergyPlusConstructionCode;

                if (!string.IsNullOrEmpty(p.TempHEROConstructionCode))
                    p.ChenathData.ConstructionCodePerSoftware["ashero"] = p.TempHEROConstructionCode;

                // Not exactly sure why (something to do with these values being set by the mapper via reflection...?) the
                // underlying JSON parts weren't updating, and thus saving to the DB wrong. This self-reassignment fixes that.
                p.ChenathData = p.ChenathData;
                p.LifeCycleData = p.LifeCycleData;
                p.EPData = p.EPData;
                p.Performance = p.Performance; // Ensure Performance JSON is updated

                if (p.Category != null)
                    p.HiddenTableColumns = Construction.AssignDefaultColumnVisibility(p.Category.ConstructionCategoryCode);
            });


            return mapper;

        }

        /// <summary>
        /// Attempts to update or insert all of the required construction/opening templates.
        /// </summary>
        /// <returns>A BulkUpsertReport indicating the overall success/failure of the operation.</returns>
        private static ProcessReport BulkUpsert<T, D>(
             IEnumerable<T> templates,
             IUnitOfWork unitOfWork,
             IMapper mapper,
             System.Data.Entity.DbSet<D> context)
             where T : ConstructionTemplateDto
             where D : class, HasExternalConstructionID
        {
            // Start inserting data into DB. Any errors are still recorded.
            var insertionErrors = new List<string>();
            int updated = 0;
            int inserted = 0;
            int failed = 0;

            foreach (var construction in templates)
            {
                var existing = context.FirstOrDefault(x => x.ExternalConstructionId == construction.ExternalConstructionId);

                D mapped = null;

                if (existing != null)
                {
                    var backup = existing.ConstructionId;
                    construction.ConstructionId = existing.ConstructionId;
                    mapper.Map(construction, existing); // Will this work without the configuration provider..?
                    existing.ConstructionId = backup;
                }
                else
                {
                    mapped = mapper.Map<D>(construction);
                    context.Add(mapped);
                }

                try
                {
                    unitOfWork.Context.SaveChanges();

                    if (existing != null)
                        updated++;
                    else
                        inserted++;
                }
                catch (Exception e)
                {
                    failed++;
                    insertionErrors.Add($"ERROR: Failed to insert template with ID '{construction.ExternalConstructionId}' into the database. Details: {e.InnerException?.InnerException?.Message}");

                    // Detach these so they won't continue to error out on subsequent calls to saveChanges...
                    if (existing != null)
                        unitOfWork.Context.Entry(existing).State = System.Data.Entity.EntityState.Detached;
                    if (mapped != null)
                        unitOfWork.Context.Entry(mapped).State = System.Data.Entity.EntityState.Detached;
                }

            }

            return new ProcessReport() { Errors = insertionErrors, Updated = updated, Inserted = inserted, Failed = failed };
        }



        private Dictionary<string, string> MapConstructionCode(string softwareCode, ChenathData c, object value)
        {
            if (c.ConstructionCodePerSoftware == null)
                c.ConstructionCodePerSoftware = new Dictionary<string, string>();

            if (value != null)
                c.ConstructionCodePerSoftware[softwareCode.ToLower()] = (string)value;

            return c.ConstructionCodePerSoftware;
        }

        // Helper method to clear Construction Database caches when data is updated
        private void ClearConstructionCaches()
        {
            // Clear filter count data cache for both construction and opening types
            CacheHandler.DeleteCacheLikeItems(CacheHandler.DataType_MultiFilterCountData, "ConstructionFilterCountData_");

            // Clear multi-filter options cache
            CacheHandler.DeleteCacheLikeItems(CacheHandler.DataType_MultiFilterOptions, "ConstructionMultiFilterOptions_");

            // Set bypass cache parameters to force fresh data on next retrieval
            SystemParameter.UpdateIntParm("BypassFilterCountDataCacheAdmin", 1);
            SystemParameter.UpdateIntParm("BypassMultiFilterOptionsCacheAdmin", 1);
        }

    }

    /// <summary>
    /// Used to provide feedback to user in regards to how the processing
    /// of the excel upload process to the database went.
    /// </summary>
    public class ProcessReport
    {

        public bool Success { get; set; }

        public HashSet<string> Warnings { get; set; }

        /// <summary>
        /// Any errros encountered during processing.
        /// </summary>
        public List<string> Errors { get; set; }

        /// <summary>
        /// Number of rows successfully updated.
        /// </summary>
        public int Updated { get; set; }

        /// <summary>
        /// Number of new rows successfully inserted into the database.
        /// </summary>
        public int Inserted { get; set; }

        public int Failed { get; set; }

        public ProcessReport()
        {
            Warnings = new HashSet<string>();
            Errors = new List<string>();
            Updated = 0;
            Inserted = 0;
            Failed = 0;
        }

        public static ProcessReport operator +(ProcessReport a, ProcessReport b)
        {
            HashSet<string> warningUnion = new HashSet<string>();
            warningUnion.UnionWith(a.Warnings);
            warningUnion.UnionWith(b.Warnings);

            return new ProcessReport()
            {
                Warnings = warningUnion,
                Errors = a.Errors.Concat(b.Errors).ToList(),
                Updated = a.Updated + b.Updated,
                Inserted = a.Inserted + b.Inserted,
                Failed = a.Failed + b.Failed
            };
        }
    }
}